<template>
  <el-container>
    <el-main>
      <h2 style="font-family: 'Microsoft YaHei',Arial,sans-serif;">
        活期理财配置
      </h2>
      <el-form :inline="true">
        <el-form-item label="币种" :model="filters">
          <el-select clearable filterable v-model="filters.asset" placeholder="<ALL>" @change="applyFilters">
            <el-option v-for="name in all_assets" :key="name" :label="name" :value="name"> </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="杠杆借币状态" :model="filters">
          <el-select v-model="filters.margin_status" clearable filterable placeholder="<ALL>" @change="applyFilters">
            <el-option v-for="(name, key) in margin_status_dict" :key="key" :label="name" :value="key"> </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="借贷借币状态" :model="filters">
          <el-select v-model="filters.loan_status" clearable filterable placeholder="<ALL>" @change="applyFilters">
            <el-option v-for="(name, key) in loan_status_dict" :key="key" :label="name" :value="key"> </el-option>
          </el-select>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="applyFilters">查询</el-button>
        </el-form-item>
        <el-form-item>
          <el-tooltip content="刷新" placement="right" :open-delay="500" :hide-after="2000">
            <el-button type="info" icon="el-icon-refresh" circle @click="get_data"></el-button>
          </el-tooltip>
        </el-form-item>
        <el-form-item>
          <el-tooltip content="添加" placement="right" :open-delay="500" :hide-after="2000">
            <el-button type="primary" icon="el-icon-plus" circle @click="handleCreate"></el-button>
          </el-tooltip>
        </el-form-item>
      </el-form>

      <el-table :data="filteredItems" stripe>
        <el-table-column prop="asset" label="币种" min-width="100"> </el-table-column>
        <el-table-column prop="min_amount" label="最小申购数" min-width="100"> </el-table-column>
        <el-table-column prop="margin_status" label="杠杆借币状态" min-width="120">
          <template slot-scope="scope"> {{ scope.row.margin_status }} </template>
        </el-table-column>
        <el-table-column prop="loan_status" label="借贷借币状态" min-width="120">
          <template slot-scope="scope"> {{ scope.row.loan_status }} </template>
        </el-table-column>
        <el-table-column prop="base_rate" label="基础年化收益率" min-width="120">
          <template slot-scope="scope"> {{ formatRate(scope.row.base_rate) }}% </template>
        </el-table-column>
        <el-table-column prop="rule_map" label="阶梯年化补贴" min-width="120">
          <template slot-scope="scope">
            <span v-if="scope.row.rule_map && scope.row.rule_map.LADDER">
              ≤{{ scope.row.rule_map.LADDER.limit }}，年化补贴{{ formatRate(scope.row.rule_map.LADDER.rate) }}%
            </span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column prop="rule_map" label="固定年化补贴" min-width="120">
          <template slot-scope="scope">
            <span v-if="scope.row.rule_map && scope.row.rule_map.FIXED">
              {{ formatRate(scope.row.rule_map.FIXED.rate) }}%
            </span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="理财状态" min-width="80">
          <template slot-scope="scope"> {{ statuses[scope.row.status] || scope.row.status }} </template>
        </el-table-column>
        <el-table-column prop="remark" label="备注" min-width="120"> </el-table-column>

        <el-table-column label="操作" min-width="200">
          <template slot-scope="scope">
            <span>
              <el-tooltip content="编辑">
                <el-button size="mini" @click="handleEdit(scope.row)" type="primary" icon="el-icon-edit" circle></el-button>
              </el-tooltip>
              <el-tooltip content="下架" v-if="scope.row.status === 'OPEN'">
                <el-button
                  size="mini"
                  type="danger"
                  @click="handle_status(scope.row, 'CLOSE')"
                  icon="el-icon-remove-outline"
                  circle
                ></el-button>
              </el-tooltip>
              <el-tooltip content="上架" v-if="scope.row.status === 'CLOSE'">
                <el-button
                  size="mini"
                  type="success"
                  @click="handle_status(scope.row, 'OPEN')"
                  icon="el-icon-circle-check"
                  circle
                ></el-button>
              </el-tooltip>
              <el-tooltip content="操作记录">
                <el-button size="mini" @click="handleOperationRecord(scope.row)" type="info" icon="el-icon-document" circle></el-button>
              </el-tooltip>
            </span>
          </template>
        </el-table-column>
      </el-table>

      <!-- 新增/编辑弹窗 -->
      <el-dialog
        :title="action === DIALOG_CREATION ? '新增活期理财币种' : '编辑活期理财币种'"
        :visible.sync="show"
        width="1000px"
        :before-close="handleClose"
      >
        <div v-if="action === DIALOG_CREATION" style="margin-bottom: 15px; color: #666;">
          新增后默认为关闭,需要手动上架,上架后下个整点开始计算收益
        </div>
        <div v-else style="margin-bottom: 15px; color: #666;">
          编辑后,下个整点开始生效
        </div>
        
        <el-form :model="submit_data" ref="submit_data" label-width="150px">
          <span v-if="action === DIALOG_CREATION">
            <el-form-item prop="asset" label="*币种" style="width: 50%" required>
              <el-select clearable filterable v-model="submit_data.asset" placeholder="请选择" @change="handleAssetChange">
                <el-option v-for="a in all_assets" :key="a" :label="a" :value="a"> </el-option>
              </el-select>
            </el-form-item>
          </span>
          <span v-else>
            <el-form-item label="*币种">
              {{ submit_data.asset }}
            </el-form-item>
          </span>

          <el-form-item prop="min_amount" label="最小申购数">
            <el-input :disabled="true" style="width: 200px;" v-model="submit_data.min_amount" size="medium"></el-input>
          </el-form-item>

          <el-form-item label="阶梯年化补贴">
            <div style="display: flex; align-items: center; gap: 8px;">
              <el-input-number 
                v-model="submit_data.ladder_limit" 
                :precision="8" 
                :controls="false"
                :min="0"
                size="small"
                style="width: 150px;">
              </el-input-number>
              <span>以内补贴年化利率</span>
              <el-input-number 
                v-model="submit_data.ladder_rate" 
                :precision="2" 
                :controls="false"
                :min="0"
                size="small"
                style="width: 120px;">
              </el-input-number>
              <span>%</span>
            </div>
          </el-form-item>

          <el-form-item prop="fixed_rate" label="固定年化补贴">
            <div style="display: flex; align-items: center; gap: 8px;">
              <el-input-number 
                v-model="submit_data.fixed_rate" 
                :precision="2" 
                :controls="false"
                :min="0"
                placeholder="请输入"
                size="small"
                style="width: 120px;">
              </el-input-number>
              <span>%</span>
            </div>
          </el-form-item>

          <el-form-item prop="remark" label="备注">
            <el-input type="textarea" v-model="submit_data.remark" placeholder="请输入" :rows="3"></el-input>
          </el-form-item>
        </el-form>

        <span slot="footer" class="dialog-footer">
          <el-button @click="show = false">取消</el-button>
          <el-button type="primary" @click="submit">确定</el-button>
        </span>
      </el-dialog>

      <!-- 上架确认弹窗 -->
      <el-dialog
        title="上架活期理财币种"
        :visible.sync="showListDialog"
        width="500px"
      >
        <div style="text-align: center; margin: 20px 0;">
          确认上架{{ listAsset }}的活期理财? 上架后下个整点开始计算收益。
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button @click="showListDialog = false">取消</el-button>
          <el-button type="primary" @click="confirmList">确定</el-button>
        </span>
      </el-dialog>

      <!-- 下架确认弹窗 -->
      <el-dialog
        title="下架活期理财币种"
        :visible.sync="showDelistDialog"
        width="400px"
      >
        <div style="text-align: center; margin: 20px 0;">
          确认下架{{ delistAsset }}的活期理财?
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button @click="showDelistDialog = false">取消</el-button>
          <el-button type="primary" @click="confirmDelist">确定</el-button>
        </span>
      </el-dialog>

      <!-- 操作记录弹窗 -->
      <el-dialog
        :title="`${operationRecordAsset}操作记录`"
        :visible.sync="showOperationRecord"
        width="80%"
      >
        <el-form :inline="true" style="margin-bottom: 15px;">
          <el-form-item label="操作字段">
            <el-select v-model="operationFilters.field" clearable filterable @change="searchOperationRecord(currentOperationRow)" placeholder="<ALL>">
              <el-option v-for="(name, key) in op_fields" :key="key" :label="name" :value="key"> </el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="searchOperationRecord(currentOperationRow)">查询</el-button>
          </el-form-item>
        </el-form>

        <el-table :data="operationRecords" stripe>
          <el-table-column prop="field" label="操作字段" min-width="80">
            <template slot-scope="scope"> {{ operationFields[scope.row.field] || scope.row.field }} </template>
          </el-table-column>
          <el-table-column prop="details" label="操作详情" min-width="200">
            <template slot-scope="scope">
              <div style="white-space: pre-wrap;">{{ scope.row.details }}</div>
            </template>
          </el-table-column>
          <el-table-column prop="operator" label="操作人" min-width="100"> </el-table-column>
          <el-table-column prop="operation_time" label="操作时间" min-width="150"> </el-table-column>
        </el-table>

        <span slot="footer" class="dialog-footer">
          <el-button @click="showOperationRecord = false">关闭</el-button>
        </span>
      </el-dialog>

      <el-backtop></el-backtop>
    </el-main>
  </el-container>
</template>

<script>
import _ from 'lodash';

const binding_url = '/api/investment/config';

export default {
  methods: {
    formatRate(rate) {
      if (!rate && rate !== 0) return 0;
      const percentage = rate * 100;
      // 如果结果是整数，返回整数；否则保留2位小数
      return Number.isInteger(percentage) ? percentage : percentage.toFixed(2);
    },
    get_data() {
      this.loading = true;
      this.$axios.get(binding_url).then(res => {
        this.loading = false;
        if (res && res.data.code === 0) {
          let data = res.data.data;
          this.items = data.items;
          this.all_assets = data.all_assets;
          this.statuses = data.statuses;
          this.config_types = data.config_types;
          this.op_fields = data.op_fields;
          this.min_amount_map = data.min_amount_map;

          // 应用当前过滤器
          this.applyFilters();
        } else {
          this.items = [];
          this.filteredItems = [];
          this.$message.error(`code: ${res.data?.code}; message: ${res.data?.message}`);
        }
      });
    },
    refresh() {
      this.get_data();
    },
    search() {
      this.applyFilters();
    },
    handleCreate() {
      this.action = this.DIALOG_CREATION;
      this.show = true;
      this.submit_data = JSON.parse(JSON.stringify(this.default_submit_data));
    },
    handleEdit(row) {
      this.action = this.DIALOG_EDIT;
      this.submit_data = {
        asset: row.asset,
        min_amount: Number(row.min_amount),
        ladder_limit: row.rule_map?.LADDER?.limit || '',
        ladder_rate: row.rule_map?.LADDER?.rate ? this.formatRate(row.rule_map.LADDER.rate) : '',
        fixed_rate: row.rule_map?.FIXED?.rate ? this.formatRate(row.rule_map.FIXED.rate) : '',
        remark: row.remark || ''
      };
      this.show = true;
    },
    handleClose() {
      this.$confirm('确认关闭？')
        .then(_ => {
          this.show = false;
          this.submit_data = {};
          this.action = '';
        })
        .catch(_ => {});
    },
    submit() {
      this.$refs['submit_data'].validate(valid => {
        if (!valid) {
          this.$alert('校验失败请修改', '校验失败请修改', {
            confirmButtonText: '确定',
          });
        } else {
          this.submit_validate_data();
        }
      });
    },
    submit_validate_data() {
      let method = 'put';
      if (this.action === this.DIALOG_CREATION) {
        method = 'post';
      }
      
      // 创建提交数据的副本，对利率进行除以100的处理
      let submitData = { ...this.submit_data };
      if (submitData.ladder_rate) {
        submitData.ladder_rate = (parseFloat(submitData.ladder_rate) / 100).toString();
      }
      if (submitData.fixed_rate) {
        submitData.fixed_rate = (parseFloat(submitData.fixed_rate) / 100).toString();
      }
      
      this.$axios[method](binding_url, submitData)
        .then(res => {
          if (res?.data?.code === 0) {
            this.$message.success('提交成功!');
            this.show = false;
            this.get_data();
          } else {
            this.$message.error(`提交失败! (code: ${res.data?.code}; message: ${res.data?.message})`);
          }
        })
        .catch(e => {
          this.$message.error(`{ statusCode: 404, message: 'Page not found' }`);
        });
    },
    handle_status(record, status) {
      if (status === 'OPEN') {
        this.listAsset = record.asset;
        this.showListDialog = true;
      } else {
        this.delistAsset = record.asset;
        this.showDelistDialog = true;
      }
    },
    confirmList() {
      this.updateStatus(this.listAsset, 'OPEN');
      this.showListDialog = false;
    },
    confirmDelist() {
      this.updateStatus(this.delistAsset, 'CLOSE');
      this.showDelistDialog = false;
    },
    updateStatus(asset, status) {
      let s = this.statuses[status];
      this.$axios
        .put(`${binding_url}/${asset}/status`, { status: status })
        .then(res => {
          if (res?.data?.code === 0) {
            this.$message.success('成功!');
            this.get_data();
          } else {
            this.$message.error(`提交失败! (code: ${res.data?.code}; message: ${res.data?.message})`);
          }
        })
        .catch(e => {
          this.$message.error(`{ statusCode: 404, message: 'Page not found' }`);
        });
    },
    handleOperationRecord(row) {
      this.operationRecordAsset = row.asset;
      this.currentOperationRow = row;
      this.showOperationRecord = true;
      this.searchOperationRecord(row);
    },
    searchOperationRecord(row) {
      if (!row.operation_logs || row.operation_logs.length === 0) {
        this.operationRecords = [];
        return;
      }
      
      // 过滤操作记录
      let filteredLogs = row.operation_logs;
      if (this.operationFilters.field) {
        filteredLogs = row.operation_logs.filter(log => 
          log.field === this.operationFilters.field
        );
      }
      
      // 转换数据格式
      this.operationRecords = filteredLogs.map(log => ({
        field: log.field,
        details: log.detail,
        operator: log.user?.name_or_email || log.user?.user_id || '未知',
        operation_time: this.$formatDate(log.created_at)
      }));
    },

    applyFilters() {
      this.filteredItems = this.items.filter(item => {
        const matchesAsset = this.filters.asset ? item.asset === this.filters.asset : true;
        const matchesMarginStatus = this.filters.margin_status ? item.margin_status === this.filters.margin_status : true;
        const matchesLoanStatus = this.filters.loan_status ? item.loan_status === this.filters.loan_status : true;
        return matchesAsset && matchesMarginStatus && matchesLoanStatus;
      });
    },
    update_router_query() {
      let query = {};
      Object.assign(query, this.filters);
      Object.keys(query).forEach(key => !query[key] && delete query[key]);
      this.$router.replace({ query: query });
    },
    handleAssetChange(asset) {
      let amount = this.min_amount_map[asset];
      if (!amount) {
        this.$message.warning('找不到该币种最新价格，请检查币种市场');
      }
      this.submit_data.min_amount = amount;
    }
  },
  created() {
    this.$sync_router_query(this, 'filters', {
      asset: String,
      margin_status: String,
      loan_status: String,
    });
  },
  mounted: function() {
    this.get_data();
  },
  data() {
    return {
      filters: {
        asset: null,
        margin_status: null,
        loan_status: null,
      },
      items: [],
      all_assets: [],
      statuses: {},
      config_types: {},
      margin_status_dict: { "开启": "开启", "关闭": "关闭" },
      loan_status_dict: { "开启": "开启", "关闭": "关闭" },
      op_fields: {},
      DIALOG_CREATION: 'CREATION',
      DIALOG_EDIT: 'EDIT',
      action: '',
      show: false,
      default_submit_data: {
        asset: '',
        min_amount: null,
        ladder_limit: '',
        ladder_rate: '',
        fixed_rate: '',
        remark: ''
      },
      submit_data: {},
      showListDialog: false,
      showDelistDialog: false,
      listAsset: '',
      delistAsset: '',
      showOperationRecord: false,
      operationRecordAsset: '',
      currentOperationRow: null,
      operationRecords: [],
      operationFilters: {
        field: null
      },
      operationFields: {
        'status': '理财状态',
        'ladder_rate': '阶梯年化补贴',
        'fixed_rate': '固定年化补贴',
        'remark': '备注'
      },
      filteredItems: [], // Added for filtered data display
      min_amount_map: {} // Added for min_amount_map
    };
  },
};
</script>
