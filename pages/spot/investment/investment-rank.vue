<template>
  <div class="table-data">
    <h2 style="font-family: 'Microsoft YaHei',<PERSON><PERSON>,sans-serif;">
      理财收益排名
    </h2>

    <el-form :inline="true">

      <el-form-item>
        <el-select label="市场" filterable v-model="search_data.asset" @change="get_data">
          <el-option v-for="name in asset_list"
                     filterable
                     :key="name"
                     :label="name"
                     :value="name">
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="时间段" :model="search_data">
        <el-select clearable v-model="search_data.time_type" placeholder="<ALL>" @change="get_data">
          <el-option v-for="name in time_option"
                     :key="name.value"
                     :label="name.label"
                     :value="name.value">
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item>
        <UserSearch v-model="search_data.id"></UserSearch>
      </el-form-item>

      <el-form-item>
        <el-tooltip content="刷新" placement="right" :open-delay="500" :hide-after="2000">
          <el-button icon="el-icon-refresh-left"
                     circle
                     @click="get_data"></el-button>
        </el-tooltip>
      </el-form-item>
    </el-form>

    <el-table v-loading="loading"
              :data="filteredRecords"
              stripe>

      <el-table-column
        prop="user_id"
        label="用户ID">
        <template slot-scope="scope">
          <el-link type="primary"
                   :href="`/users/user-details?id=${scope.row.user_id}`"
                   :underline="false"
                   target="_blank">
            {{ scope.row.user_id }}
          </el-link>
        </template>
      </el-table-column>

      <el-table-column
        prop="asset"
        label="理财币种">
      </el-table-column>

      <el-table-column
        prop="total_amount"
        label="累计收益">
      </el-table-column>

      <el-table-column
        label="基础收益"
        :formatter="row => $formatNumber(row.BASE || 0)"
        prop="BASE"
      >
      </el-table-column>

      <el-table-column
        label="阶梯补贴"
        :formatter="row => $formatNumber(row.LADDER || 0)"
        prop="LADDER"
      >
      </el-table-column>

      <el-table-column
        label="固定补贴"
        :formatter="row => $formatNumber(row.FIXED || 0)"
        prop="FIXED"
      >
      </el-table-column>
     

      <el-table-column
        prop="rank"
        label="排名">
      </el-table-column>

    </el-table>

    <el-backtop></el-backtop>

  </div>
</template>

<script>

  import moment from "moment";
  import UserSearch from "~/components/user/UserSearch";

  export default {
    components: {UserSearch},
    methods: {
      handle_oper(row) {
        this.search_keyword = row.user_id;
        this.search_data.id = row.user_id;
        this.get_data();
      },
      get_data() {
        this.loading = true;
        this.$axios.get(
          '/api/investment/rank',
          {params: this.search_data}
        ).then(
          res => {
            if (res.data.code === 0) {
              let data = res.data.data;
              this.records = data.records;
              this.asset_list = data.asset_list;
              this.loading = false;
            } else {
              this.$message.error(`code: ${res.data?.code}; message: ${res.data?.message}`);
            }
          }
        ).catch(err => {
          this.$message.error(`刷新失败! (${err})`);
        });
      },
    },
    mounted() {
      this.get_data();
    },
    data() {
      return {
        search_data: {
          id: null,
          asset: 'BTC',
          time_type: 'day_7',
        },
        records: [],
        asset_list: [],
        search_keyword: null,
        loading: true,
        search_result: [],
        time_option: [
          {"label": "最近7天", "value": "day_7"},
          {"label": "最近30天", "value": "day_30"},
        ]
      }
    },
    computed: {
      filteredRecords() {
        if (!this.search_data.id) {
          return this.records;
        }
        return this.records.filter(record => 
          record.user_id && record.user_id.toString().includes(this.search_data.id.toString())
        );
      }
    }
  }
</script>
