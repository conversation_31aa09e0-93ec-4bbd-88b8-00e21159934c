<template>
  <div class="table-data">
    <h2 style="font-family: 'Microsoft YaHei', <PERSON>l, sans-serif">
      理财-理财记录
    </h2>

    <el-form :inline="true">
      <el-form-item label="用户" :model="search_data">
        <UserSearch
          v-model="search_data.user_id"
          :refresh_method="get_data"
          @change="get_data(true)"
        ></UserSearch>
      </el-form-item>
      <el-form-item label="币种" :model="search_data">
        <el-select
          label="币种"
          filterable
          clearable
          v-model="search_data.asset"
          @change="get_data(true)"
        >
          <el-option
            v-for="name in assets"
            filterable
            :key="name"
            :label="name"
            :value="name"
          >
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="类型" :model="search_data">
        <el-select
          clearable
          v-model="search_data.history_type"
          placeholder="<ALL>"
          @change="get_data(true)"
        >
          <el-option
            v-for="(v, k) in history_types"
            :key="k"
            :label="v"
            :value="k"
          >
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="维度" :model="search_data" v-if="search_data.history_type == 'INTEREST'">
        <el-select
          clearable
          v-model="search_data.time_type"
          placeholder="<ALL>"
          @change="get_data(true)"
        >
          <el-option
            v-for="(v, k) in interest_time_types"
            :key="k"
            :label="v"
            :value="k"
          >
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="利息类型" :model="search_data" v-if="search_data.history_type == 'INTEREST' && search_data.time_type == 'HOUR'">
        <el-select
          clearable
          v-model="search_data.interest_type"
          placeholder="<ALL>"
          @change="get_data(true)"
        >
          <el-option
            v-for="(v, k) in interest_types"
            :key="k"
            :label="v"
            :value="k"
          >
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="时间范围" :model="search_data">
        <el-date-picker
          v-model="time_range"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          value-format="timestamp"
          @change="handle_time_range_change"
        >
        </el-date-picker>
      </el-form-item>

      <el-form-item label="划转类型" :model="search_data" v-if="search_data.history_type == 'TRANSFER'">
        <el-select
          clearable
          v-model="search_data.trans_type"
          placeholder="<ALL>"
          @change="get_data(true)"
        >
          <el-option
            v-for="(v, k) in trans_types"
            :key="k"
            :label="v"
            :value="k"
          >
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="状态" :model="search_data" v-if="search_data.history_type == 'TRANSFER'">
        <el-select
          clearable
          v-model="search_data.status"
          placeholder="<ALL>"
          @change="get_data(true)"
        >
          <el-option
            v-for="(v, k) in trans_statuses"
            :key="k"
            :label="v"
            :value="k"
          >
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="状态" :model="search_data" v-if="search_data.history_type == 'INTEREST'">
        <el-select
          clearable
          v-model="search_data.interest_status"
          placeholder="<ALL>"
          @change="get_data(true)"
        >
          <el-option
            v-for="(v, k) in interest_statuses"
            :key="k"
            :label="v"
            :value="k"
          >
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-tooltip
          content="刷新"
          placement="right"
          :open-delay="500"
          :hide-after="2000"
        >
          <el-button
            icon="el-icon-refresh-left"
            circle
            @click="get_data"
          ></el-button>
        </el-tooltip>
      </el-form-item>
    </el-form>

    <el-table v-loading="loading" :data="records" stripe>
      <el-table-column
        label="创建时间"
        :formatter="(row) => $formatDate(row.created_at)"
        prop="created_at"
      >
      </el-table-column>
      <el-table-column prop="user_id" label="用户ID">
        <template slot-scope="scope">
          <el-link
            type="primary"
            :href="`/users/user-details?id=${scope.row.user_id}`"
            :underline="false"
            target="_blank"
          >
            {{ scope.row.user_id }}
          </el-link>
        </template>
      </el-table-column>
      <el-table-column prop="asset" label="币种"> </el-table-column>
      <el-table-column
        prop="type"
        label="类型"
        :formatter="(row) => search_data.history_type == 'TRANSFER' ? trans_types[row.opt_type] : '利息收入'"
      >
      </el-table-column>

            <el-table-column
        prop="interest_type"
        label="利息类型"
        v-if="search_data.history_type == 'INTEREST' && search_data.time_type == 'HOUR'"
        :formatter="(row) => interest_types[row.interest_type]"
      >
      </el-table-column>

      <el-table-column
        prop="balance"
        label="快照余额"
        v-if="search_data.history_type == 'INTEREST' && search_data.time_type == 'HOUR'"
      >
      </el-table-column>
      <el-table-column
        prop="rate"
        label="快照利率"
        v-if="search_data.history_type == 'INTEREST' && search_data.time_type == 'HOUR'"
        :formatter="(row) => row.rate ? $formatPercent(row.rate, 2) : '-'"
      >
      </el-table-column>

      <el-table-column
        prop="amount"
        :formatter="(row) => search_data.history_type == 'TRANSFER' ? Math.abs(row.amount) : row.interest_amount"
        label="数量"
      >
      </el-table-column>

      <el-table-column
        prop="status"
        label="状态"
        :formatter="(row) => {
          if (search_data.history_type == 'TRANSFER') {
            return trans_statuses[row.status] || row.status;
          } else if (search_data.history_type == 'INTEREST') {
            if (search_data.time_type == 'HOUR') {
              return '已记录';
            } else {
              return interest_statuses[row.status] || row.status;
            }
          }
          return row.status;
        }"
      >
      </el-table-column>

    </el-table>
    <CursorPagination ref="page"
                      :cursor.sync="search_data.cursor"
                      :has-next="has_next"
                      :page-size.sync="search_data.limit"
                      :page-sizes="[20, 50, 100, 200]"
                      @next="handle_page_change">
    </CursorPagination>

    <el-backtop></el-backtop>
  </div>
</template>
  
  <script>
import moment from "moment";
import UserSearch from "~/components/user/UserSearch";
import CursorPagination from "~/components/CursorPagination.vue";

export default {
  components: { UserSearch, CursorPagination },
  methods: {
    handle_oper(row) {
      this.search_data.user_id = row.user_id;
      this.get_data();
    },
    get_data(refresh = false) {
      if (refresh) {
        this.search_data.cursor = null;
      }
      this.loading = true;
      this.$axios
        .get("/api/investment/record", { params: this.search_data })
        .then((res) => {
          if (res.data.code === 0) {
            let data = res.data.data;
            this.records = data.records;
            this.assets = data.assets;
            this.status_map = data.status_map;
            this.trans_statuses = data.trans_statuses;
            this.interest_statuses = data.interest_statuses;
            this.trans_types = data.trans_types;
            this.history_types = data.history_types;
            this.interest_time_types = data.interest_time_types;
            this.interest_types = data.interest_types;
            this.has_next = data.has_next;
            this.$refs.page.pushNextCursor(data.cursor);
            this.loading = false;
          } else {
            this.$message.error(
              `code: ${res.data?.code}; message: ${res.data?.message}`
            );
          }
        })
        .catch((err) => {
          this.$message.error(`刷新失败! (${err})`);
        });
    },
    handle_page_change() {
      this.get_data(false);
    },
    handle_time_range_change() {
      if (this.time_range && this.time_range.length === 2) {
        this.search_data.start_at = this.time_range[0];
        this.search_data.end_at = this.time_range[1];
      } else {
        this.search_data.start_at = null;
        this.search_data.end_at = null;
      }
      this.get_data(true);
    },
  },
  mounted() {
    this.get_data();
  },
  data() {
    return {
      search_data: {
        cursor: null,
        limit: 50,
        user_id: null,
        asset: null,
        status: null,
        interest_status: null,
        history_type: "INTEREST",
        time_type: "DAY",
        interest_type: null,
        start_at: null,
        end_at: null,
      },
      records: [],
      assets: [],
      search_keyword: null,
      loading: true,
      search_result: [],
      status_map: {},
      trans_statuses: {},
      interest_statuses: {},
      trans_types: {},
      history_types: {},
      interest_time_types: {},
      interest_types: {},
      has_next: false,
      time_range: null,
    };
  },
  computed: {},
};
</script>
  