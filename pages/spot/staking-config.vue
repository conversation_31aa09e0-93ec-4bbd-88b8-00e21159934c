<template>
  <div class="table-data">
    <h2 style="font-family: 'Microsoft YaHei',Arial,sans-serif;">
      超额质押目标值配置
      <el-tooltip placement="right" :open-delay="500">
        <div slot="content">
          <p>币种：支持的质押币种</p>
          <p>链上质押总数量：链上当前已生效的质押数量 + 质押中 - 赎回中</p>
          <p>用户质押总数量：用户当前已生效的质押数量</p>
          <p>超额质押数量：链上质押总数量-用户质押总数量</p>
          <p>超额质押目标值：平台需要维持的超额质押数量，支持配置</p>
        </div>
        <i class="el-icon-question"></i>
      </el-tooltip>
    </h2>
    <el-row style="margin-bottom: 30px"> 更新时间 : {{ $formatDate(this.report_at) }} </el-row>

    <el-table v-loading="loading" :data="tableData">
      <el-table-column prop="asset" label="币种">
      </el-table-column>
      <el-table-column prop="system_staking" label="链上质押总数量">
      </el-table-column>
      <el-table-column prop="amount" label="用户质押总数量">
      </el-table-column>
      <el-table-column prop="bufsize" label="超额质押数量">
      </el-table-column>
      <el-table-column prop="target_bufsize" label="超额质押目标值">
      </el-table-column>
      <el-table-column label="操作" width="120">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="primary"
            @click="handleEdit(scope.row)">编辑</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- Edit Dialog -->
    <el-dialog title="编辑超额质押目标值" :visible.sync="dialogVisible" width="30%">
      <el-form :model="editForm" label-width="120px">
        <el-form-item label="币种">
          <span>{{ editForm.asset }}</span>
        </el-form-item>
        <el-form-item label="超额质押目标值" required>
          <el-input-number v-model="editForm.target_bufsize" :min="0" :required="true"></el-input-number>
        </el-form-item>
      </el-form>
      <UserWebAuthn ref="UserWebAuthn" :operation_type="operation_type" />
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleSave">确 定</el-button>
      </span>
    </el-dialog>

    <el-backtop></el-backtop>
  </div>
</template>

<script>
import UserWebAuthn from '@/components/UserWebAuthn.vue';

export default {
  components: { UserWebAuthn },
  methods: {
    get_data() {
      this.loading = true;
      this.$axios.get(
        '/api/staking/statistics',
      ).then(
        res => {
          if (res.data.code === 0) {
            let data = res.data.data;
            // Combine chain_data and user_data to create table data
            this.tableData = data.chain_data.map(chain => {
              const userData = data.user_data.find(user => user.asset === chain.asset) || {};
              return {
                asset: chain.asset,
                system_staking: Number(chain.system_effective_amount) + Number(chain.system_pending_staking) - Number(chain.system_pending_unstaking),
                amount: Number(userData.effective_asset_amount) + Number(userData.pending_staking_asset_amount),
                bufsize: chain.bufsize,
                target_bufsize: chain.target_bufsize
              };
            });
            this.report_at = data.report_at;
            this.loading = false;
          } else {
            this.$message.error(`code: ${res.data?.code}; message: ${res.data?.message}`);
          }
        }
      ).catch(err => {
        this.$message.error(`刷新失败! (${err})`);
      });
    },

    handleEdit(row) {
      this.editForm = { ...row };
      this.dialogVisible = true;
    },

    async handleWebAuthn(success_callback) {
      const UserWebAuthn = this.$refs["UserWebAuthn"];
      await UserWebAuthn.run();
      await UserWebAuthn.handleWebAuthn().then(() => {
        if (UserWebAuthn.success) {
          if (success_callback) {
            success_callback();
          }
          return true;
        } else {
          this.$message.error("WebAuthn校验失败!");
          return false;
        }
      }).catch(err => {
        this.$message.error(`WebAuthn校验失败! ${err}`);
        return false;
      });
    },

    handleSave() {
      if (!this.editForm.target_bufsize || this.editForm.target_bufsize < 0) {
        this.$message.error("请填写正确的超额质押目标值");
        return;
      }
      // 保存前应已通过WebAuthn校验
      this.$axios.post('/api/staking/config', {
        asset: this.editForm.asset,
        target_bufsize: this.editForm.target_bufsize
      }, {
        headers: {
          'WebAuthn-Token': this.$refs["UserWebAuthn"]?.webauthn_token || '',
          'Operate-Type': this.operation_type
        }
      }).then(res => {
        if (res.data.code === 0) {
          this.$message.success('保存成功');
          this.dialogVisible = false;
          this.get_data(); // Refresh data
        } 
        else if (res.data.code === 132) {
          this.handleWebAuthn(() => {
            this.handleSave();
          });
        }
        else {
          this.$message.error(`保存失败: ${res.data.message}`);
        }
      }).catch(err => {
        this.$message.error(`保存失败! (${err})`);
      });
    },
  },

  mounted() {
    this.get_data();
  },

  data() {
    return {
      tableData: [],
      report_at: null,
      loading: false,
      dialogVisible: false,
      editForm: {
        asset: '',
        target_bufsize: 0
      },
      operation_type: 'STAKING_CONFIG',
    }
  }
}
</script> 