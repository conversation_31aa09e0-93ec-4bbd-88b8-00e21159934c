<template>
    <div class="table-data">
      <el-row type="flex" justify="space-between" align="middle">
        <el-col :span="22">
          <h2 style="font-family: 'Microsoft YaHei',Arial,sans-serif;">
            风险用户列表
          </h2>
        </el-col>
    </el-row>
  
    <el-form :inline="true">
        <el-form-item label="风险模型">
            <el-select v-model="filters.risk_model_name" @change="get_data">
                <el-option v-for="risk_model in risk_models"
                            :key="risk_model.risk_model_name"
                            :label="risk_model.risk_model_name_cn"
                            :value="risk_model.risk_model_name">
                </el-option>
            </el-select>
        </el-form-item>

        <el-form-item label="用户">
            <UserSearch
            v-model="filters.user_id"
            :refresh_method="get_data"
            @change="get_data"
            ></UserSearch>
        </el-form-item>

        <el-form-item label="风控起始时间">
            <el-date-picker
                v-model="filters.start_time"
                type="datetime"
                value-format="yyyy-MM-dd HH:mm:ss"
                placeholder="时间">
            </el-date-picker>
        </el-form-item>

        <el-form-item label="风控截止时间">
            <el-date-picker
                v-model="filters.end_time"
                type="datetime"
                value-format="yyyy-MM-dd HH:mm:ss"
                placeholder="时间">
            </el-date-picker>
        </el-form-item>

        <el-form-item>
            <el-tooltip
            content="下载人工标注模板"
            placement="right"
            :open-delay="500"
            :hide-after="2000"
            >
                <el-button
                    type="primary"
                    icon="el-icon-download"
                    circle
                    style="margin-left: 10px"
                    @click="download_template"
                ></el-button>
            </el-tooltip>
        </el-form-item>

        <el-form-item>
        <el-tooltip
          content="上传人工标注"
          placement="right"
          :open-delay="500"
          :hide-after="2000"
        >
          <el-upload
            ref="upload"
            action="/api/anti-fraud/user-risk-info/manual-annotation/import"
            name="file"
            :data="{ risk_model_name: filters.risk_model_name }"
            :headers="{ AUTHORIZATION: $cookies.get('admin_token') }"
            :before-upload="before_upload"
            :on-success="(res) => upload_success(res, false)"
            :on-error="upload_error"
            :show-file-list="false"
          >
            <el-button type="primary" icon="el-icon-upload" circle></el-button>
          </el-upload>
        </el-tooltip>
      </el-form-item>

      <el-form-item>
            <el-tooltip
            content="导出交叉对比表"
            placement="right"
            :open-delay="500"
            :hide-after="2000"
            >
                <el-button
                    icon="el-icon-download"
                    circle
                    type="success"
                    @click="handle_export_risk_user_info"
                ></el-button>
            </el-tooltip>
        </el-form-item>

        <el-form-item>
            <el-tooltip content="刷新" placement="right" :open-delay="500" :hide-after="2000">
                <el-button icon="el-icon-refresh-left"
                            circle
                            @click="handle_page_refresh"></el-button>
            </el-tooltip>
        </el-form-item>

      </el-form>

      <div v-if="filters.risk_model_name">
            <el-table :data="items"
            v-loading ="loading"
                stripe>

                <el-table-column label="用户ID" prop="user_id">
            <template slot-scope="scope">
            <el-link
                :href="'/users/user-details?id=' + scope.row.user_id"
                type="primary"
                target="_blank"
                :underline="false"
                style="
                width: 100%;
                font-weight: normal;
                display: inline-block;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                "
            >
                {{ scope.row.user_id }}
            </el-link>
            </template>
        </el-table-column>

        <el-table-column label="邮箱" prop="user_email">
            <template slot-scope="scope">
            <el-link
                :href="'/users/user-details?id=' + scope.row.user_id"
                type="primary"
                target="_blank"
                :underline="false"
                style="
                width: 100%;
                font-weight: normal;
                display: inline-block;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                "
            >
                {{ scope.row.user_email }}
            </el-link>
            </template>
        </el-table-column>

        <el-table-column
          prop="first_risk_time"
          label="首次风控时间"
        >
        </el-table-column>

        <el-table-column
          prop="first_risk_reason"
          label="首次风险特征"
        >
            <template slot-scope="scope">
                <div class="line-break">{{scope.row.first_risk_reason}}</div>
            </template>
        </el-table-column>

        <el-table-column
          prop="last_risk_time"
          label="最近计算时间"
        >
        </el-table-column>

        <el-table-column
          prop="last_risk_reason"
          label="最近风险特征"
        >
            <template slot-scope="scope">
                <div class="line-break">{{scope.row.last_risk_reason}}</div>
            </template>
        </el-table-column>

        <el-table-column
          prop="manual_annotation"
          label="人工标注"
        >
        </el-table-column>

        <el-table-column
          prop="manual_annotation_at"
          label="人工标注时间"
        >
        </el-table-column>

        <el-table-column
          prop="manual_annotation_reason"
          label="人工标注备注"
        >
            <template slot-scope="scope">
                <div class="line-break">{{scope.row.manual_annotation_reason}}</div>
            </template>
        </el-table-column>
  
        <el-table-column
          prop="operation"
          label="操作">
          <template slot-scope="scope">
            <el-tooltip content="人工标注" placement="right" :open-delay="500" :hide-after="2000">
              <el-button size="mini" type="primary" icon="el-icon-edit" circle
                         @click="handle_manual_annotation(scope.row)"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
            </el-table>

            <el-pagination
                :current-page.sync="filters.page"
                :page-size.sync="filters.limit"
                :page-sizes="[10, 50, 100, 200, 500]"
                :total="total"
                @size-change="get_data"
                @current-change="get_data"
                layout="total, sizes, prev, pager, next, jumper"
            >
            </el-pagination>
        </div>
        <div v-else>
            <p style="text-align: center;">请选择一个风险模型以查看数据</p>
        </div>

      <el-dialog title="人工标注风险用户":visible.sync="manual_annotation_dialog_show" width="50%">
        <el-form ref="manual_annotation_dialog_show">
            <el-form-item label="标注类型">
                <el-select v-model="manual_annotation_data.manual_annotation" @change="get_data">
                    <el-option v-for="(label, value) in extra.manual_annotation_type"
                            :key="value"
                            :label="label"
                            :value="value">
                </el-option>
                </el-select>
            </el-form-item>

            <el-form-item label="标注时间">
                <el-date-picker
                    v-model="manual_annotation_data.manual_annotation_at"
                    type="datetime"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    placeholder="时间">
                </el-date-picker>
            </el-form-item>

            <el-form-item label="标注原因">
                <el-input
                    type="textarea"
                    v-model="manual_annotation_data.manual_annotation_reason"
                ></el-input>
            </el-form-item>

        </el-form>
        <span slot="footer" class="dialog-footer">
            <el-button @click="manual_annotation_dialog_close">取 消</el-button>
            <el-button type="primary" @click="submit_manual_annotation">确 定</el-button>
        </span>
      </el-dialog>

      <el-dialog title="导出交叉对比表":visible.sync="export_risk_user_info_dialog_show" width="50%">
        <el-form ref="export_risk_user_info_dialog_show">
            <el-form-item label="起始时间">
                <el-date-picker
                    v-model="export_filters.start_time"
                    type="datetime"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    placeholder="时间">
                </el-date-picker>
            </el-form-item>
            <el-form-item label="截止时间">
                <el-date-picker
                    v-model="export_filters.end_time"
                    type="datetime"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    placeholder="时间">
                </el-date-picker>
            </el-form-item>
        </el-form>
        <span slot="footer" class="dialog-footer">
            <el-button @click="export_risk_user_info_close">取 消</el-button>
            <el-button type="primary" @click="export_risk_user_info">确 定</el-button>
        </span>
      </el-dialog>
  
      <el-backtop></el-backtop>
    </div>
  </template>
  
  <style>
    .line-break {
     white-space: pre-line;
    }
  </style>
  
  <script>
  import UserSearch from "@/components/user/UserSearch";
  import moment from "moment";
  
  export default {
    components: {
        UserSearch,
    },
    methods: {
        get_filter_data() {
            this.$axios.get(
                '/api/anti-fraud/risk/model/list'
            ).then(
                res => {
                if (res.data.code === 0) {
                    this.risk_models = res.data.data;
                } else {
                    this.$message.error(`code: ${res.data?.code}; message: ${res.data?.message}`);
                }
                }
            ).catch(err => {
                this.$message.error(`获取风险模型失败! (${err})`);
            });
        },

      get_data() {
        this.loading = true;
        this.$axios.get(
          '/api/anti-fraud/user-risk-info', {params: this.filters}
        ).then(
          res => {
            if (res.data.code === 0) {
              this.items = res.data.data.items;
              this.total = res.data.data.total;
              this.extra = res.data.data.extra;
              this.loading = false;
            } else {
              this.$message.error(`code: ${res.data?.code}; message: ${res.data?.message}`);
              this.loading = false;
            }
          }
        ).catch(err => {
          this.$message.error(`刷新失败! (${err})`);
        });
      },
      handle_page_refresh() {
        this.get_data();
      },
      download_template() {
        this.$download_from_url('/api/anti-fraud/user-risk-info/manual-annotation/import', "user-risk-info-manual-annotation.xlsx");
      },
      handle_export_risk_user_info() {
        this.export_risk_user_info_dialog_show = true
      },
      export_risk_user_info() {
        if (!this.export_filters.start_time || !this.export_filters.end_time) {
            this.$message.warning("请选择起始和截止时间");
            return;
        }

        if (new Date(this.export_filters.start_time) > new Date(this.export_filters.end_time)) {
            this.$message.warning("起始时间不能晚于截止时间");
            return;
        }

        if (!this.filters.risk_model_name) {
            this.$message.warning("请选择风险模型");
            return;
        }

        this.export_filters.risk_model_name = this.filters.risk_model_name
        let params = { ...this.export_filters };
        this.$download_from_url(
            "/api/anti-fraud/user-risk-info/export", "user_risk_info.csv", params
        );
        this.export_filters.start_time = null;
        this.export_filters.end_time = null;
        this.export_risk_user_info_dialog_show = false
      },
      export_risk_user_info_close() {
        this.export_filters.start_time = null;
        this.export_filters.end_time = null;
        this.export_risk_user_info_dialog_show = false;
      },

      handle_manual_annotation(row) {
        this.manual_annotation_dialog_show = true
        this.manual_annotation_data.risk_model_name = this.filters.risk_model_name
        this.manual_annotation_data.user_id = row.user_id
      },
      submit_manual_annotation() {
        if (!this.manual_annotation_data.manual_annotation) {
            this.$message.warning("请选择标注类型");
            return false;
        }
        if (!this.manual_annotation_data.manual_annotation_at) {
            this.$message.warning("请选择标注时间");
            return false;
        }
        if (!this.manual_annotation_data.manual_annotation_reason) {
            this.$message.warning("请输入标注原因");
            return false;
        }

        this.$axios.post(
          '/api/anti-fraud/user-risk-info', this.manual_annotation_data
        ).then(res => {
                this.$message.info(`标注成功!`);
            }
        ).catch(err => {
          this.$message.error(`标注失败! (${err})`);
        });
        this.manual_annotation_dialog_close();
      },
      manual_annotation_dialog_close() {
        this.manual_annotation_data = {
            risk_model_name: null,
            user_id: null,
            manual_annotation: null,
            manual_annotation_at: null,
            manual_annotation_reason: '',
        }
        this.manual_annotation_dialog_show = false
      },

      format_date(timestamp, pattern = 'YYYY-MM-DD HH:mm:ss') {
        return moment(Number(timestamp) * 1000).format(pattern);
      },
      before_upload(file) {
        let name = file.name;
        if (
            !name.endsWith(".xlsx") &&
            !name.endsWith(".xls") &&
            !name.endsWith("csv")
        ) {
            this.$message.error("只能上传excel表格");
            return false;
        }
        },
      upload_success(res, fil) {
        if (res?.code === 0) {
            this.$message.success(`导入成功`);
            this.refresh();
        } else {
            this.$message.error(
            `上传失败! (code: ${res?.code}; message: ${res?.message})`
            );
        }
      },
      upload_error(err) {
        this.$message.error(`上传失败! (${err})`);
      },
    },
    created() {
    },
    mounted() {
        this.get_filter_data();
        if (this.filters.risk_model_name) {
            this.get_data();
        } 
    },
    data() {
      return {
        filters: {
          risk_model_name: null,
          start_time: null,
          end_time: null,
          user_id: null,
          page: 1,
          limit: 50,
        },
        export_filters: {
          risk_model_name: null,
          start_time: null,
          end_time: null,
        },
        risk_models: [],
        manual_annotation_dialog_show: false,
        manual_annotation_user_id: null,
        export_risk_user_info_dialog_show: false,
        items: [],
        loading: true,
        extra: {
            risk_model_name: null,
            risk_model_name_cn: null,
            manual_annotation_type: null,
        },
        total: 0,
        manual_annotation_data: {
            risk_model_name: null,
            user_id: null,
            manual_annotation: null,
            manual_annotation_at: null,
            manual_annotation_reason: '',
        },
      }
    },
  }
  </script>
