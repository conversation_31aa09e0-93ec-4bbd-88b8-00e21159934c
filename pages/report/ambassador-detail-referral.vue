<template>
    <el-container>
      <el-main>
        <h2 style="font-family: 'Microsoft YaHei',Arial,sans-serif;">
          大使返佣记录明细
        </h2>
        <el-tabs v-model="search_data.report_type" type="card"  @tab-click="search(true)">
          <el-tab-pane label="日报" name="daily"></el-tab-pane>
          <el-tab-pane label="月报" name="monthly"></el-tab-pane>
        </el-tabs>
        <el-form :inline="true" :model="search_data">
          

          <el-form-item label="用户ID">
            <UserSearch v-model="search_data.referree_id" :refresh_method="search" @change="search"></UserSearch>
          </el-form-item>
          <el-form-item label="大使邮箱">
            <UserSearch v-model="search_data.user_id" :refresh_method="search" @change="search"></UserSearch>
          </el-form-item>

          <template v-if="search_data.report_type === 'daily'">
            <el-form-item prop="start_date" label="开始时间">
              <el-date-picker
                required
                v-model="search_data.start_date"
                type="date"
                value-format="yyyy-MM-dd"
                placeholder="开始时间">
              </el-date-picker>
            </el-form-item>
            <el-form-item prop="end_date" label="结束时间">
              <el-date-picker
                required
                v-model="search_data.end_date"
                type="date"
                value-format="yyyy-MM-dd"
                placeholder="结束时间">
              </el-date-picker>
            </el-form-item>
          </template>
  
          <template v-if="search_data.report_type === 'monthly'">
            <el-form-item prop="start_date" label="开始时间">
              <el-date-picker
                required
                v-model="search_data.start_date"
                type="month"
                value-format="yyyy-MM-01"
                placeholder="开始时间">
              </el-date-picker>
            </el-form-item>
            <el-form-item prop="end_date" label="结束时间">
              <el-date-picker
                required
                v-model="search_data.end_date"
                type="month"
                value-format="yyyy-MM-01"
                placeholder="结束时间">
              </el-date-picker>
            </el-form-item>
          </template>
          
          <el-form-item>
            <el-button type="primary" @click="search">查询</el-button>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="download">导出</el-button>
          </el-form-item>
        </el-form>
  
        <el-table
          :data="items"
          style="width: 100%">
  
          <el-table-column
            label="日期"
            prop="report_date"
             :formatter="row => format_date(row.report_date)"
          >
          </el-table-column>

          <el-table-column
            prop="referree_id"
            label="用户ID">
            <template slot-scope="scope">
              <el-link :href="'/users/user-details?id=' + scope.row.referree_id"
                       type="primary"
                       target="_blank"
                       :underline="false"
                       style="width: 100%; font-weight: normal; display: inline-block; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
                {{ scope.row.referree_id }}
              </el-link>
            </template>
          </el-table-column>
          
          <el-table-column
            prop="user_email"
            label="大使邮箱">
            <template slot-scope="scope">
              <el-link :href="'/users/user-details?id=' + scope.row.user_id"
                       type="primary"
                       target="_blank"
                       :underline="false"
                       style="width: 100%; font-weight: normal; display: inline-block; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
                {{ scope.row.user_email }}
              </el-link>
            </template>
          </el-table-column>
        
          <el-table-column label="币币交易额（USD）"
                           :formatter="row => $formatNumber(row.spot_trade_usd, 2)"
                           prop="spot_trade_usd">
          </el-table-column>
          <el-table-column label="合约交易额（USD）"
                           :formatter="row => $formatNumber(row.perpetual_trade_usd, 2)"
                           prop="perpetual_trade_usd">
          </el-table-column>
          <el-table-column label="总交易额（USD）"
                           :formatter="row => $formatNumber(row.total_trade_usd, 2)"
                           prop="total_trade_usd">
          </el-table-column>
          <el-table-column label="币币手续费（USD）"
                           :formatter="row => $formatNumber(row.spot_fee_usd, 2)"
                           prop="spot_fee_usd">
          </el-table-column>
          <el-table-column label="合约手续费（USD）"
                           :formatter="row => $formatNumber(row.perpetual_fee_usd, 2)"
                           prop="perpetual_fee_usd">
          </el-table-column>
          <el-table-column label="总手续费（USD）"
                           :formatter="row => $formatNumber(row.total_fee_usd, 2)"
                           prop="total_fee_usd">
          </el-table-column>
          <el-table-column label="返佣金额（USDT）"
                           prop="refer_total_amount"
                           :formatter="row => $formatNumber(row.refer_total_amount, 8)"
          >
          </el-table-column>
        </el-table>
  
        <el-pagination :current-page.sync="search_data.page"
                       :page-size.sync="search_data.limit"
                       :total="total"
                       @current-change="search"
                       :page-sizes="[50, 100]"
                       :hide-on-single-page="true"
                       layout="total, sizes, prev, pager, next, jumper"
        >
        </el-pagination>
        <el-backtop></el-backtop>
      </el-main>
    </el-container>
  </template>
  
  
  <script>
    import moment from "moment";
    import UserSearch from '../../components/user/UserSearch';
  
    const binding_url = "/api/report/ambassador/referral-detail-report";
  
    export default {
      components: { UserSearch },
      watchQuery: ['user_id', 'referree_id', 'report_type', 'start_date', 'end_date', 'page', 'limit'],
      key: to => to.fullPath,
      asyncData({app, query}) {
        query.limit = query.limit ? parseInt(query.limit) : 50
        query.page = query.page ? parseInt(query.page) : 1
        let new_query = _.clone(query);
        if(!new_query.hasOwnProperty("report_type")) {
          new_query.report_type = 'daily';
        }
        return app.$axios["get"](binding_url, {params: new_query})
          .then((res) => {
            return {
              total: res.data.data.total,
              items: res.data.data.items,
              search_data: _.clone(new_query),
            }
          })
          .catch((e) => {
          })
      },
      methods: {
        format_date(timestamp, pattern = 'YYYY-MM-DD') {
      return moment(Number(timestamp) * 1000).format(pattern);
    },
    
        search(reset=true) {
          if(reset === true) {
            this.search_data.page = 1;
          }
          this.$router.push({path: this.$route.path, query: this.search_data});
        },
        download() {
          let params = {...this.search_data, export: 1};
          delete params['page'];
          delete params['limit'];
          this.$axios.get(binding_url, {params: params, responseType: 'blob'}).then(res => {
            const content_type = res.headers['content-type'];
            const url = window.URL.createObjectURL(new Blob([res.data], content_type? {type: content_type} : {}));
            const a = document.createElement(('a'));
            a.href = url;
            document.body.appendChild(a);
            const content_disposition = res.headers['content-disposition'];
            a.download = content_disposition? content_disposition.split('filename=')[1] : 'temp.xlsx';
            a.click();
            window.URL.revokeObjectURL(url);
          });
        },
      },
      data() {
        return {
          search_data: {},
          total_data: {},
        //   pickerOptions: {
        //     disabledDate(time) {
        //       return time.getTime() > Date.now();
        //     }
        //   }
        }
      },
    }
  
  </script>
  
  
  <style>
  
  .el-table {
    display: flex;
    flex-direction: column;
  }
  
  .el-table__body-wrapper {
    order: 1;
  }
  
  </style>
  