<template>
  <el-container>
    <el-main>
      <h2 style="font-family: 'Microsoft YaHei',Arial,sans-serif;">
        子代理返佣记录
      </h2>

      <el-tabs v-model="search_data.report_type" type="card" @tab-click="search(true)">
        <el-tab-pane label="日报" name="daily"></el-tab-pane>
        <el-tab-pane label="月报" name="monthly"></el-tab-pane>
      </el-tabs>

      <el-form :inline="true" :model="search_data">
        <template v-if="search_data.report_type === 'daily'">
          <el-form-item prop="start_date" label="开始时间">
            <el-date-picker required v-model="search_data.start_date" type="date" value-format="yyyy-MM-dd"
              :picker-options="pickerOptions" placeholder="开始时间">
            </el-date-picker>
          </el-form-item>
          <el-form-item prop="end_date" label="结束时间">
            <el-date-picker required v-model="search_data.end_date" type="date" value-format="yyyy-MM-dd"
              :picker-options="pickerOptions" placeholder="结束时间">
            </el-date-picker>
          </el-form-item>
        </template>

        <template v-if="search_data.report_type === 'monthly'">
          <el-form-item prop="start_date" label="开始时间">
            <el-date-picker required v-model="search_data.start_date" type="month" value-format="yyyy-MM-01"
              :picker-options="pickerOptions" placeholder="开始时间">
            </el-date-picker>
          </el-form-item>
          <el-form-item prop="end_date" label="结束时间">
            <el-date-picker required v-model="search_data.end_date" type="month" value-format="yyyy-MM-01"
              :picker-options="pickerOptions" placeholder="结束时间">
            </el-date-picker>
          </el-form-item>
        </template>

        <el-form-item label="子代理">
          <UserSearch v-model="search_data.keyword"></UserSearch>
        </el-form-item>

        <el-form-item label="上级用户">
          <UserSearch v-model="search_data.parent_id"></UserSearch>
        </el-form-item>

        <el-form-item label="所属商务">
          <UserSearch v-model="search_data.bus_user_id"></UserSearch>
        </el-form-item>

        <el-form-item label="代理级别">
          <el-input clearable v-model="search_data.tree_height" placeholder="请输入级别整数"></el-input>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="search">查询</el-button>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="download">导出</el-button>
        </el-form-item>
      </el-form>

      <el-table :data="items" show-summary :summary-method="getTotal" style="width: 100%">

        <el-table-column label="日期" prop="report_date" min-width="100px">
        </el-table-column>
        <el-table-column prop="email" label="子代理" show-overflow-tooltip min-width="160px">
          <template slot-scope="scope">
            <el-link :href="'/users/user-details?id=' + scope.row.user_id" type="primary" target="_blank"
              :underline="false"
              style="width: 100%; font-weight: normal; display: inline-block; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
              {{ scope.row.email }}
            </el-link>
          </template>
        </el-table-column>
        <el-table-column prop="bus_user_id" label="所属商务" show-overflow-tooltip min-width="160px">
          <template slot-scope="scope">
            <el-link :href="'/users/user-details?id=' + scope.row.bus_user_id" type="primary" target="_blank"
              :underline="false"
              style="width: 100%; font-weight: normal; display: inline-block; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
              {{ scope.row.bus_user_name }}
            </el-link>
          </template>
        </el-table-column>
        <el-table-column label="代理级别" prop="tree_height" min-width="100px">
        </el-table-column>
        <el-table-column label="推荐人数" prop="refer_count" min-width="100px">
        </el-table-column>
        <el-table-column label="被邀请人交易人数" prop="deal_count" min-width="135px">
        </el-table-column>
        <el-table-column label="被邀请人币币交易量（USD）" :formatter="row => $formatNumber(row.spot_trade_usd, 2)"
          min-width="200px" prop="spot_trade_usd">
        </el-table-column>
        <el-table-column label="被邀请人合约交易量（USD）" :formatter="row => $formatNumber(row.perpetual_trade_usd, 2)"
          min-width="200px" prop="perpetual_trade_usd">
        </el-table-column>
        <el-table-column label="被邀请人币币手续费（USD）" :formatter="row => $formatNumber(row.spot_fee_usd, 2)" min-width="200px"
          prop="spot_fee_usd">
        </el-table-column>
        <el-table-column label="被邀请人合约手续费（USD）" :formatter="row => $formatNumber(row.perpetual_fee_usd, 2)"
          min-width="200px" prop="perpetual_fee_usd">
        </el-table-column>
        <el-table-column label="自己收到返佣（USDT）" prop="refer_total_amount" min-width="170px"
          :formatter="row => $formatNumber(row.refer_total_amount, 8)">
        </el-table-column>
        <el-table-column prop="parent_refer_list" label="返佣链路（USDT）" show-overflow-tooltip min-width="450px">
          <template slot-scope="scope">
            <div>
              <!-- 展示前3项 + 省略号 -->
              <el-popover placement="right" trigger="hover" width="400" popper-class="full-path-popover">
                <!-- 全部内容放在 popover slot -->
                <div v-for="(path, index) in scope.row.parent_refer_list" :key="'all_' + index"
                  style="display: flex; align-items: center; margin-bottom: 4px;">
                  <el-link :href="path['user_id'] | user_link" target="_blank" type="primary" :underline="false"
                    style="margin-right: 5px; width: 400px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap; display: inline-block;">
                    {{ path['level_str'] }}: {{ path['email'] }}, {{ path['amount'] }}
                  </el-link>
                </div>

                <!-- 触发内容 -->
                <div slot="reference" v-if="scope.row.parent_refer_list.length > 0">
                  <div v-for="(path, index) in scope.row.parent_refer_list.slice(0, 3)" :key="'short_' + index"
                    style="display: flex; align-items: center;">
                    <el-link :href="path['user_id'] | user_link" target="_blank" type="primary" :underline="false"
                      style="margin-right: 5px; width: 400px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap; display: inline-block;">
                      {{ path['level_str'] }}: {{ path['email'] }}, {{ path['amount'] }}
                    </el-link>
                  </div>
                  <!-- 超出提示 -->
                  <div v-if="scope.row.parent_refer_list.length > 3" style="color: #999;">...更多</div>
                </div>
              </el-popover>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <el-pagination :current-page.sync="search_data.page" :page-size.sync="search_data.limit" :total="total"
        @current-change="search" :page-sizes="[50, 100]" :hide-on-single-page="true"
        layout="total, sizes, prev, pager, next, jumper">
      </el-pagination>
      <el-backtop></el-backtop>
    </el-main>
  </el-container>
</template>


<script>
import UserSearch from "../../components/user/UserSearch";

const binding_url = "/api/report/bus-amb/tree-amb-referral-report";

export default {
  components: { UserSearch },
  watchQuery: ['keyword', 'bus_user_id', 'report_type', 'start_date', 'end_date', 'page', 'limit', 'tree_height', 'parent_id'],
  key: to => to.fullPath,
  asyncData({ app, query }) {
    query.limit = query.limit ? parseInt(query.limit) : 50
    query.page = query.page ? parseInt(query.page) : 1
    if (query.tree_height === '') {
      query.tree_height = null;
    }
    if (query.parent_id === '') {
      query.parent_id = null;
    }
    let new_query = _.clone(query);
    if (!new_query.hasOwnProperty("report_type")) {
      new_query.report_type = 'daily';
    }
    return app.$axios["get"](binding_url, { params: new_query })
      .then((res) => {
        return {
          total_data: res.data.data.total_data,
          total: res.data.data.count,
          items: res.data.data.items,
          search_data: _.clone(new_query),
        }
      })
      .catch((e) => {
      })
  },
  methods: {
    getTotal(param) {
      let t = this.total_data
      return ["ALL", "", "", "", t.refer_count, t.deal_count, t.spot_trade_usd,
        t.perpetual_trade_usd, t.spot_fee_usd, t.perpetual_fee_usd, t.refer_total_amount, t.parent_total_amount]
    },
    search(reset = true) {
      if (reset === true) {
        this.search_data.page = 1;
      }
      this.$router.push({ path: this.$route.path, query: this.search_data });
    },
    download() {
      let params = { ...this.search_data, export: 1 };
      delete params['page'];
      delete params['limit'];
      this.$axios.get(binding_url, {
        params: params,
        responseType: 'blob'
      }).then(res => {
        const content_type = res.headers['content-type'];
        const url = window.URL.createObjectURL(new Blob([res.data], content_type ? { type: content_type } : {}));
        const a = document.createElement(('a'));
        a.href = url;
        document.body.appendChild(a);
        const content_disposition = res.headers['content-disposition'];
        a.download = content_disposition ? content_disposition.split('filename=')[1] : 'temp.xlsx';
        a.click();
        window.URL.revokeObjectURL(url);
      });
    },
  },
  data() {
    return {
      search_data: {},
      total_data: {},
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now();
        }
      }
    }
  },
}

</script>


<style>
.el-table {
  display: flex;
  flex-direction: column;
}

.el-table__body-wrapper {
  order: 1;
}
</style>
