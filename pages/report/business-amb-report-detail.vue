<template>
  <el-container>
    <el-main>
      <div class="header">
        <h2 style="font-family: 'Microsoft YaHei',Arial,sans-serif;">
          商务大使明细报表
        </h2>
        <div class="sub-header">
          <span>报表类型：{{ 
            query.report_type === 'daily' ? '日报' : 
            query.report_type === 'weekly' ? '周报' :
            query.report_type === 'monthly' ? '月报' :
            query.report_type === 'quarterly' ? '季报' : '日报'
          }}</span>
        </div>
      </div>

      <el-form :inline="true" :model="search_data.sort_field" class="search-bar">
        <el-form-item label="排序">
          <el-select v-model="search_data.sort_field" placeholder="请选择排序">
            <el-option v-for="field in sort_fields" :key="field.value" :label="field.label" :value="field.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="日期">
          <el-date-picker
            v-model="search_data.report_date"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择日期"
            style="width: 160px;"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="search">查询</el-button>
        </el-form-item>
        <el-form-item>
          <el-button icon="el-icon-download" circle @click="download"></el-button>
        </el-form-item>
      </el-form>

      <el-table :data="items" v-loading="loading" style="width: 100%">
        <el-table-column prop="report_date" label="日期" width="110" />
        <el-table-column prop="business_team" label="团队" width="100" />
        <el-table-column prop="bus_user_count" label="商务数量" width="100" />
        <el-table-column prop="bus_amb_count" label="商务大使数量" width="120" />
        <el-table-column prop="new_bus_amb_count" label="新增大使" width="100" />
        <el-table-column prop="refer_count" label="refer总人数" width="120" />
        <el-table-column prop="new_refer_count" label="refer注册人数" width="120">
          <template slot-scope="scope">
            <el-row>{{ $formatNumber(scope.row.new_refer_count, 2) }}</el-row>
            <el-row>{{ scope.row.invitee_percent }}</el-row>
          </template>
        </el-table-column>
        <el-table-column prop="deal_count" label="refer交易人数" width="120">
          <template slot-scope="scope">
            <el-row>{{ $formatNumber(scope.row.deal_count, 2) }}</el-row>
            <el-row>{{ scope.row.trade_percent }}</el-row>
          </template>
        </el-table-column>
        <el-table-column prop="new_deal_count" label="新增refer交易人数" width="140">
          <template slot-scope="scope">
            <el-row>{{ $formatNumber(scope.row.new_deal_count, 2) }}</el-row>
            <el-row>{{ scope.row.new_trade_percent }}</el-row>
          </template>
        </el-table-column>
        <el-table-column prop="deal_usd" label="refer交易总额(USD)" width="160">
          <template slot-scope="scope">
            {{ $formatNumber(scope.row.deal_usd, 2) }}
          </template>
        </el-table-column>
        <el-table-column prop="fee_usd" label="refer贡献手续费(USD)" width="160">
          <template slot-scope="scope">
            {{ $formatNumber(scope.row.fee_usd, 2) }}
          </template>
        </el-table-column>
        <el-table-column prop="refer_amb_count" label="收到返佣大使" width="120" />
        <el-table-column prop="refer_amb_amount" label="大使返佣金额(USD)" width="140">
          <template slot-scope="scope">
            {{ $formatNumber(scope.row.refer_amb_amount, 2) }}
          </template>
        </el-table-column>
        <el-table-column prop="refer_agent_count" label="收到返佣代理" width="120" />
        <el-table-column prop="refer_agent_amount" label="代理返佣金额(USD)" width="140">
          <template slot-scope="scope">
            {{ $formatNumber(scope.row.refer_agent_amount, 2) }}
          </template>
        </el-table-column>
        <el-table-column prop="average_refer_rate" label="平均返佣比例" width="120">
          <template slot-scope="scope">
            {{ $formatNumber(scope.row.average_refer_rate, 2) }}%
          </template>
        </el-table-column>
        <el-table-column
          v-if="search_data.report_type !== 'daily'"
          label="收到返佣商务" prop="refer_bus_count" :render-header="renderHeader"
          column-key="收到返佣商务：收到返佣商务">
        </el-table-column>

        <el-table-column
          v-if="search_data.report_type !== 'daily'"
          label="商务返佣金额" prop="refer_bus_amount" :render-header="renderHeader"
          :formatter="row => $formatNumber(row.refer_bus_amount, 2)"
          column-key="商务返佣金额：商务返佣金额">
        </el-table-column>
      </el-table>

    </el-main>
  </el-container>
</template>

<script>
const binding_url = "/api/report/bus-amb/ambassador-report-detail";

export default {
  data() {
    return {
      loading: false,
      items: [],
      query: {},
      search_data: {
        report_date: '',
        sort_field: 'refer_count',
        report_type: 'daily',
      },
      sort_fields: [
        { label: '商务数量', value: 'bus_user_count' },
        { label: '商务大使数量', value: 'bus_amb_count' },
        { label: '新增大使', value: 'new_bus_amb_count' },
        { label: 'refer总人数', value: 'refer_count' },
        { label: 'refer注册人数', value: 'new_refer_count' },
        { label: 'refer交易人数', value: 'deal_count' },
        { label: '新增refer交易人数', value: 'new_deal_count' },
        { label: 'refer交易总额(USD)', value: 'deal_usd' },
        { label: 'refer贡献手续费(USD)', value: 'fee_usd' },
        { label: '收到返佣大使', value: 'refer_amb_count' },
        { label: '大使返佣金额(USD)', value: 'refer_amb_amount' },
        { label: '收到返佣代理', value: 'refer_agent_count' },
        { label: '代理返佣金额(USD)', value: 'refer_agent_amount' },
        { label: '平均返佣比例', value: 'average_refer_rate' }
      ]
    };
  },
  watchQuery: ["report_date", "sort_field"],
  key: to => to.fullPath,
  asyncData({ app, query }) {
    return app.$axios.get(binding_url, { params: query })
      .then(res => {
        if (res.data.code === 0) {
          return {
            items: res.data.data.items,
            query: query,
            search_data: {
              report_date: query.report_date,
              sort_field: query.sort_field || 'refer_count',
              report_type: query.report_type || 'daily'
            }
          };
        }
        return {
          items: [],
          query: query,
          search_data: {
            report_date: query.report_date,
            sort_field: query.sort_field || 'refer_count',
            report_type: query.report_type || 'daily'
          }
        };
      })
      .catch(() => {
        return {
          items: [],
          query: query,
          search_data: {
            report_date: query.report_date,
            sort_field: query.sort_field || 'refer_count',
            report_type: query.report_type || 'daily'
          }
        };
      });
  },
  methods: {
    
    search() {
      this.loading = true;
      const params = {
        ...this.query,
        report_date: this.search_data.report_date,
        sort_field: this.search_data.sort_field
      };
      this.$router.push({ path: this.$route.path, query: params });
    },
    download() {
      const params = {
        ...this.query,
        report_date: this.search_data.report_date,
        sort_field: this.search_data.sort_field,
        export: 1
      };
      this.$axios.get(binding_url, {
        params: params,
        responseType: 'blob'
      }).then(res => {
        const content_type = res.headers['content-type'];
        const url = window.URL.createObjectURL(new Blob([res.data], content_type ? {type: content_type} : {}));
        const a = document.createElement('a');
        a.href = url;
        document.body.appendChild(a);
        const content_disposition = res.headers['content-disposition'];
        a.download = content_disposition ? content_disposition.split('filename=')[1] : 'detail.xlsx';
        a.click();
        window.URL.revokeObjectURL(url);
      });
    }
  }
};
</script>

<style scoped>
.header {
  margin-bottom: 20px;
}

.sub-header {
  margin-top: 10px;
  color: #606266;
}

.sub-header span {
  margin-right: 20px;
}

.el-main {
  padding: 20px;
}

.search-bar {
  margin-bottom: 20px;
}
</style> 