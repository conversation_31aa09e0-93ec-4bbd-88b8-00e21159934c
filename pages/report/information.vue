<template>
  <el-container>
    <el-main>
      <h2 style="font-family: 'Microsoft YaHei',Arial,sans-serif;">
        {{ report_type === REPORT_TYPE_VIEW ? '内容浏览(月报)' : '内容发布(月报)' }}
        <el-tooltip placement="right" :open-delay="500">
          <div v-if="report_type === REPORT_TYPE_VIEW" slot="content">
            <p>阅读量：周期内，用户访问详情页即上报阅读量+1</p>
            <p>浏览量：周期内，当前内容展示在前端页面可视区域并且进入对应页面请求后端接口（客户端为当前访问期间），当前内容浏览次数+1，不满足前提条件反复浏览浏览量不+1</p>
            <p>资讯总浏览量：文章纬度浏览量总和（快讯、要闻、币种资讯）</p>
            <p>资讯总阅读量：文章纬度阅读量总和（快讯、要闻、币种资讯）</p>
            <p>环比：(当月-上月)/上月*100%</p>
          </div>
          <div v-if="report_type === REPORT_TYPE_PUBLISH" slot="content">
            <p>文章发布时间在周期内的文章数量</p>
            <p>平台快讯：平台原创快讯文章数量</p>
            <p>资讯：第三方资讯数量</p>
          </div>
          <i class="el-icon-question"></i>
        </el-tooltip>
      </h2>
      <el-form :inline="true" :model="search_data">
        <el-form-item prop="lang" label="语言">
          <el-select v-model="search_data.lang" @change="get_data()" filterable clearable>
            <el-option v-for="(value, key) in language_dict" :key="key" :label="value" :value="key"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item prop="start_date" label="开始时间">
          <el-date-picker
            v-model="search_data.start_date"
            type="month"
            value-format="yyyy-MM-01"
            :picker-options="pickerOptions"
            @change="update_date()"
            placeholder="开始时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item prop="end_date" label="结束时间">
          <el-date-picker
            v-model="search_data.end_date"
            type="month"
            value-format="yyyy-MM-01"
            :picker-options="pickerOptions"
            @change="update_date()"
            placeholder="结束时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button icon="el-icon-download" circle type="success" @click="download"></el-button>
        </el-form-item>
      </el-form>

      <template v-if="report_type === REPORT_TYPE_VIEW">
        <el-table :data="show_items" border style="width: 100%" v-loading="loading">
          <el-table-column
            prop="report_date"
            label="日期"
            :formatter="row => $formatDate(row.report_date, 'YYYY-MM')">
          </el-table-column>
          <el-table-column prop="total_view" label="资讯总浏览量">
            <template slot-scope="scope">
              <span>{{ scope.row.total_view }}</span>
              <br>
              <span>{{ '(' + scope.row.total_view_growth + '%)' }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="total_read" label="资讯总阅读量">
            <template slot-scope="scope">
              <span>{{ scope.row.total_read }}</span>
              <br>
              <span>{{ '(' + scope.row.total_read_growth + '%)' }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="news_view" label="快讯浏览量">
            <template slot-scope="scope">
              <span>{{ scope.row.news_view }}</span>
              <br>
              <span>{{ '(' + scope.row.news_view_growth + '%)' }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="news_read" label="快讯阅读量">
            <template slot-scope="scope">
              <span>{{ scope.row.news_read }}</span>
              <br>
              <span>{{ '(' + scope.row.news_read_growth + '%)' }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="article_view" label="要闻浏览量">
            <template slot-scope="scope">
              <span>{{ scope.row.article_view }}</span>
              <br>
              <span>{{ '(' + scope.row.article_view_growth + '%)' }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="article_read" label="要闻阅读量">
            <template slot-scope="scope">
              <span>{{ scope.row.article_read }}</span>
              <br>
              <span>{{ '(' + scope.row.article_read_growth + '%)' }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="coin_view" label="币种资讯浏览量">
            <template slot-scope="scope">
              <span>{{ scope.row.coin_view }}</span>
              <br>
              <span>{{ '(' + scope.row.coin_view_growth + '%)' }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="coin_read" label="币种资讯阅读量">
            <template slot-scope="scope">
              <span>{{ scope.row.coin_read }}</span>
              <br>
              <span>{{ '(' + scope.row.coin_read_growth + '%)' }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="academy_read" label="学院阅读量">
            <template slot-scope="scope">
              <span>{{ scope.row.academy_read }}</span>
              <br>
              <span>{{ '(' + scope.row.academy_read_growth + '%)' }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="insight_read" label="洞见阅读量">
            <template slot-scope="scope">
              <span>{{ scope.row.insight_read }}</span>
              <br>
              <span>{{ '(' + scope.row.insight_read_growth + '%)' }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="insight_rss_read" label="洞见(RSS)阅读量">
            <template slot-scope="scope">
              <span>{{ scope.row.insight_rss_read }}</span>
              <br>
              <span>{{ '(' + scope.row.insight_rss_read_growth + '%)' }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="blog_read" label="博客阅读量">
            <template slot-scope="scope">
              <span>{{ scope.row.blog_read }}</span>
              <br>
              <span>{{ '(' + scope.row.blog_read_growth + '%)' }}</span>
            </template>
          </el-table-column>
        </el-table>
      </template>

      <template v-if="report_type === REPORT_TYPE_PUBLISH">
        <el-table :data="show_items" border style="width: 100%" v-loading="loading">
          <el-table-column
            prop="report_date"
            label="日期"
            :formatter="row => $formatDate(row.report_date, 'YYYY-MM')">
          </el-table-column>
          <el-table-column prop="self_platform_news" label="平台快讯">
            <template slot-scope="scope">
              <span>{{ scope.row.self_platform_news }}</span>
              <br>
              <span>{{ '(' + scope.row.self_platform_news_growth + '%)' }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="academy" label="学院">
            <template slot-scope="scope">
              <span>{{ scope.row.academy }}</span>
              <br>
              <span>{{ '(' + scope.row.academy_growth + '%)' }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="insight" label="洞见">
            <template slot-scope="scope">
              <span>{{ scope.row.insight }}</span>
              <br>
              <span>{{ '(' + scope.row.insight_growth + '%)' }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="blog" label="博客">
            <template slot-scope="scope">
              <span>{{ scope.row.blog }}</span>
              <br>
              <span>{{ '(' + scope.row.blog_growth + '%)' }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="information_news" label="资讯(快讯)">
            <template slot-scope="scope">
              <span>{{ scope.row.information_news }}</span>
              <br>
              <span>{{ '(' + scope.row.information_news_growth + '%)' }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="information_article" label="资讯(要闻)">
            <template slot-scope="scope">
              <span>{{ scope.row.information_article }}</span>
              <br>
              <span>{{ '(' + scope.row.information_article_growth + '%)' }}</span>
            </template>
          </el-table-column>
        </el-table>
      </template>
    </el-main>
  </el-container>
</template>


<script>
import XLSX from "xlsx";
import moment from "moment/moment";

const view_url = '/api/report/information/view';
const publish_url = '/api/report/information/publish';

export default {
  watch: {
    "$route.query"(val) {
      this.report_type = val.report_type;
      if (this.report_type !== this.REPORT_TYPE_VIEW && this.report_type !== this.REPORT_TYPE_PUBLISH) {
        this.report_type = this.REPORT_TYPE_VIEW;
      }
      this.get_data();
    }
  },
  methods: {
    get_data() {
      this.loading = true;
      let params = {
        lang: this.search_data.lang,
        type: 'MONTHLY',
      };
      let url = this.report_type === this.REPORT_TYPE_VIEW ? view_url : publish_url;
      this.$axios.get(url, {params: params}).then(res => {
        this.loading = false;
        if (res?.data?.code === 0) {
          let data = res.data.data;
          this.language_dict = data.langs;
          this.items = data.items;
          this.update_date();
        } else {
          this.loading = false;
          this.$message.error(`code: ${res.data?.code}; message: ${res.data?.message}`);
        }
      });
    },
    update_date() {
      let start_ts = 0;
      let end_ts = 9999999999;
      if (this.search_data.start_date !== null) {
        start_ts = new Date(this.search_data.start_date).getTime() / 1000;
      }
      if (this.search_data.end_date !== null) {
        end_ts = new Date(this.search_data.end_date).getTime() / 1000;
      }
      this.show_items = [];
      this.items.forEach(item => {
        if (start_ts <= item.report_date && item.report_date <= end_ts) {
          this.show_items.push(item);
        }
      });
    },
    formatDate(timestamp) {
      let fmt = "YYYY-MM";
      return moment(Number(timestamp * 1000)).format(fmt);
    },
    download() {
      const _XLSX = require('xlsx');
      let wb = _XLSX.utils.book_new();
      let ws_data = []
      this.show_items.forEach(item => {
        if (this.report_type === this.REPORT_TYPE_VIEW) {
          let obj = {
            '日期': this.formatDate(item.report_date),
            '资讯总浏览量': item.total_view,
            '资讯总浏览量环比增长': item.total_view_growth + '%',
            '资讯总阅读量': item.total_read,
            '资讯总阅读量环比增长': item.total_read_growth + '%',
            '快讯浏览量': item.news_view,
            '快讯浏览量环比增长': item.news_view_growth + '%',
            '快讯阅读量': item.news_read,
            '快讯阅读量环比增长': item.news_read_growth + '%',
            '要闻浏览量': item.article_view,
            '要闻浏览量环比增长': item.article_view_growth + '%',
            '要闻阅读量': item.article_read,
            '要闻阅读量环比增长': item.article_read_growth + '%',
            '币种资讯浏览量': item.coin_view,
            '币种资讯浏览量环比增长': item.coin_view_growth + '%',
            '币种资讯阅读量': item.coin_read,
            '币种资讯阅读量环比增长': item.coin_read_growth + '%',
            '学院阅读量': item.academy_read,
            '学院阅读量环比增长': item.academy_read_growth + '%',
            '洞见阅读量': item.insight_read,
            '洞见阅读量环比增长': item.insight_read_growth + '%',
            '洞见订阅阅读量': item.insight_rss_read,
            '洞见订阅阅读量环比增长': item.insight_rss_read_growth + '%',
            '博客阅读量': item.blog_read,
            '博客阅读量环比增长': item.blog_read_growth + '%',
          }
          ws_data.push(obj)
        } else {
          let obj = {
            '日期': this.formatDate(item.report_date),
            '平台快讯': item.self_platform_news,
            '平台快讯环比增长': item.self_platform_news_growth + '%',
            '学院': item.academy,
            '学院环比增长': item.academy_growth + '%',
            '洞见': item.insight,
            '洞见环比增长': item.insight_growth + '%',
            '博客': item.blog,
            '博客环比增长': item.blog_growth + '%',
            '资讯(快讯)': item.information_news,
            '资讯(快讯)环比增长': item.information_news_growth + '%',
            '资讯(要闻)': item.information_article,
            '资讯(要闻)环比增长': item.information_article_growth + '%',
          }
          ws_data.push(obj)
        }
      })
      let sheet = _XLSX.utils.json_to_sheet(ws_data);
      _XLSX.utils.book_append_sheet(wb, sheet);
      if (ws_data.length !== 0) {
        let filename = this.report_type === this.REPORT_TYPE_VIEW ? '内容浏览(月报).xlsx' : '内容发布(月报).xlsx'
        _XLSX.writeFile(wb, filename);
      } else {
        this.$message.error("当前所选内容数据为空")
      }
    },
  },
  mounted() {
    this.report_type = this.$route.query.report_type;
    if (this.report_type !== this.REPORT_TYPE_VIEW && this.report_type !== this.REPORT_TYPE_PUBLISH) {
      this.report_type = this.REPORT_TYPE_VIEW;
    }
    this.get_data();
  },
  data() {
    return {
      REPORT_TYPE_VIEW: 'view',
      REPORT_TYPE_PUBLISH: 'publish',
      report_type: null,
      loading: false,
      language_dict: {},
      search_data: {
        lang: null,
        start_date: null,
        end_date: null,
      },
      items: [],
      show_items: [],
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now();
        }
      }
    }
  },
}

</script>
