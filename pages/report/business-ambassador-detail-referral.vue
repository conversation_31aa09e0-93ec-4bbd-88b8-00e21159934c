<template>
  <el-container>
    <el-main>
      <h2 style="font-family: 'Microsoft YaHei',Arial,sans-serif;">
        代理返佣记录明细
      </h2>
      <el-tabs v-model="search_data.report_type" type="card" @tab-click="search(true)">
        <el-tab-pane label="日报" name="daily"></el-tab-pane>
        <el-tab-pane label="月报" name="monthly"></el-tab-pane>
      </el-tabs>
      <el-form :inline="true" :model="search_data">
        <el-form-item label="用户ID">
            <UserSearch
              v-model="search_data.referree_id"
            ></UserSearch>
          </el-form-item>
          <el-form-item label="代理邮箱">
              <UserSearch
                v-model="search_data.user_id"
              ></UserSearch>
            </el-form-item>
            <el-form-item label="商务">
              <UserSearch
                v-model="search_data.bus_user_id"
              ></UserSearch>
            </el-form-item>
          <el-form-item label="团队">
            <el-select v-model="search_data.bus_team_id" placeholder="请选择团队" clearable>
              <el-option v-for="(value, key) in team_dic" :key="key" :label="value" :value="key"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="代理类型">
            <el-select v-model="search_data.agent_type" placeholder="请选择代理类型" clearable>
              <el-option v-for="(value, key) in agent_type_dic" :key="key" :label="value" :value="key"></el-option>
            </el-select>
          </el-form-item>
        <template v-if="search_data.report_type === 'daily'">
          <el-form-item prop="start_date" label="开始时间">
            <el-date-picker
              required
              v-model="search_data.start_date"
              type="date"
              value-format="yyyy-MM-dd"
              :picker-options="pickerOptions"
              placeholder="开始时间">
            </el-date-picker>
          </el-form-item>
          <el-form-item prop="end_date" label="结束时间">
            <el-date-picker
              required
              v-model="search_data.end_date"
              type="date"
              value-format="yyyy-MM-dd"
              :picker-options="pickerOptions"
              placeholder="结束时间">
            </el-date-picker>
          </el-form-item>
        </template>

        <template v-if="search_data.report_type === 'monthly'">
          <el-form-item prop="start_date" label="开始时间">
            <el-date-picker
              required
              v-model="search_data.start_date"
              type="month"
              value-format="yyyy-MM-01"
              :picker-options="pickerOptions"
              placeholder="开始时间">
            </el-date-picker>
          </el-form-item>
          <el-form-item prop="end_date" label="结束时间">
            <el-date-picker
              required
              v-model="search_data.end_date"
              type="month"
              value-format="yyyy-MM-01"
              :picker-options="pickerOptions"
              placeholder="结束时间">
            </el-date-picker>
          </el-form-item>
        </template>

        <el-form-item>
          <el-button type="primary" @click="search">查询</el-button>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="download">下载</el-button>
        </el-form-item>
      </el-form>

      <el-table
        :data="items"
        style="width: 100%">

        <el-table-column
          label="日期"
          prop="report_date"
        >
        </el-table-column>
        <el-table-column
        prop="referree_id"
        label="用户ID">
        <template slot-scope="scope">
          <el-link :href="'/users/user-details?id=' + scope.row.referree_id"
                   type="primary"
                   target="_blank"
                   :underline="false"
                   style="width: 100%; font-weight: normal; display: inline-block; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
            {{ scope.row.referree_id }}
          </el-link>
        </template>
      </el-table-column>

        <el-table-column
          prop="user_email"
          label="代理邮箱"
          show-overflow-tooltip>
          <template slot-scope="scope">
            <el-link :href="'/users/user-details?id=' + scope.row.user_id"
                     type="primary"
                     target="_blank"
                     :underline="false"
                     style="width: 100%; font-weight: normal; display: inline-block; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
              {{ scope.row.user_email }}
            </el-link>
          </template>
        </el-table-column>
        <el-table-column
          prop="bus_name"
          label="商务"
          show-overflow-tooltip>
          <template slot-scope="scope">
            <el-link :href="'/users/user-details?id=' + scope.row.bus_user_id"
                     type="primary"
                     target="_blank"
                     :underline="false"
                     style="width: 100%; font-weight: normal; display: inline-block; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
              {{ scope.row.bus_name }}
            </el-link>
          </template>
        </el-table-column>
        <el-table-column
          prop="bus_team"
          label="团队"
          show-overflow-tooltip>
        </el-table-column>
        <el-table-column label="代理类型" prop="agent_type">
        </el-table-column>
        
        <el-table-column label="币币交易额（USD）"
                         :formatter="row => $formatNumber(row.spot_trade_usd, 2)"
                         prop="spot_trade_usd">
        </el-table-column>
        <el-table-column label="合约交易额（USD）"
                         :formatter="row => $formatNumber(row.perpetual_trade_usd, 2)"
                         prop="perpetual_trade_usd">
        </el-table-column>
        <el-table-column label="总交易额（USD）"
                         :formatter="row => $formatNumber(row.total_trade_usd, 2)"
                         prop="total_trade_usd">
        </el-table-column>

        <el-table-column label="币币手续费（USD）"
                         :formatter="row => $formatNumber(row.spot_fee_usd, 2)"
                         prop="spot_fee_usd">
        </el-table-column>
        <el-table-column label="合约手续费（USD）"
                         :formatter="row => $formatNumber(row.perpetual_fee_usd, 2)"
                         prop="perpetual_fee_usd">
        </el-table-column>
        <el-table-column label="总手续费（USD）"
                         :formatter="row => $formatNumber(row.total_fee_usd, 2)"
                         prop="total_fee_usd">
        </el-table-column>
        <el-table-column label="返佣金额（USDT）"
                         prop="refer_total_amount"
                         :formatter="row => $formatNumber(row.refer_total_amount, 8)"
        >
        </el-table-column>
      </el-table>

      <el-pagination :current-page.sync="search_data.page"
                     :page-size.sync="search_data.limit"
                     :total="total"
                     @current-change="search"
                     :page-sizes="[50, 100]"
                     :hide-on-single-page="true"
                     layout="total, sizes, prev, pager, next, jumper"
      >
      </el-pagination>
      <el-backtop></el-backtop>
    </el-main>
  </el-container>
</template>


<script>
import UserSearch from "../../components/user/UserSearch";
import moment from "moment";

const binding_url = "/api/report/bus-amb/ambassador-detail-referral-report";

export default {
  components: {UserSearch},
  watchQuery: ['referree_id', 'bus_user_id', 'report_type', 'start_date', 'end_date', 'page', 'limit', 'user_id', 'bus_team_id', 'agent_type'],
  key: to => to.fullPath,
  asyncData({app, query}) {
    query.limit = query.limit ? parseInt(query.limit) : 50
    query.page = query.page ? parseInt(query.page) : 1
    let new_query = _.clone(query);
    if (!new_query.hasOwnProperty("report_type")) {
      new_query.report_type = 'daily';
    }
    
    // 过滤掉空值和null值
    const cleanQuery = {};
    Object.keys(new_query).forEach(key => {
      const value = new_query[key];
      if (value !== null && value !== undefined && value !== '') {
        cleanQuery[key] = value;
      }
    });
    
    return app.$axios["get"](binding_url, {params: cleanQuery})
      .then((res) => {
        return {
        team_dic: res.data.data.extras.team_dic,
        agent_type_dic: res.data.data.extras.agent_type_dic,
        total: res.data.data.total,
        items: res.data.data.items,
        search_data: _.clone(new_query)
      }
        
      })
      .catch((e) => {
      })
  },
  methods: {
    // 过滤掉空值和null值的工具方法
    filterEmptyValues(obj) {
      const cleanObj = {};
      Object.keys(obj).forEach(key => {
        const value = obj[key];
        if (value !== null && value !== undefined && value !== '') {
          cleanObj[key] = value;
        }
      });
      return cleanObj;
    },
    
    search(reset = true) {
      if (reset === true) {
        this.search_data.page = 1;
      }
      
      // 过滤掉空值和null值
      const cleanQuery = this.filterEmptyValues(this.search_data);
      this.$router.push({path: this.$route.path, query: cleanQuery});
    },
    download() {
      // 过滤掉空值和null值
      const cleanParams = this.filterEmptyValues(this.search_data);
      
      let params = {...cleanParams, export: 1};
      delete params['page'];
      delete params['limit'];
      this.$axios.get(binding_url, {
        params: params,
        responseType: 'blob'
      }).then(res => {
        const content_type = res.headers['content-type'];
        const url = window.URL.createObjectURL(new Blob([res.data], content_type ? {type: content_type} : {}));
        const a = document.createElement(('a'));
        a.href = url;
        document.body.appendChild(a);
        const content_disposition = res.headers['content-disposition'];
        a.download = content_disposition ? content_disposition.split('filename=')[1] : 'temp.xlsx';
        a.click();
        window.URL.revokeObjectURL(url);
      });
    },
  },
  data() {
    return {
      team_dic: {},
      agent_type_dic: {},
      search_data: {},
      total_data: {},
      items: [],
      total: 0,
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now();
        }
      }
    }
  },
}

</script>


<style>

.el-table {
  display: flex;
  flex-direction: column;
}

.el-table__body-wrapper {
  order: 1;
}

</style>
