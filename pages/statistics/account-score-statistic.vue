<template>
  <div class="market-maker-monitor">
    <h2 style="font-family: 'Microsoft YaHei', Arial, sans-serif">
      {{ title }}做市商账户监控
    </h2>
    
    <!-- 更新时间显示 -->
    <div class="update-time">
      更新时间: {{ format_date(updateTime) }}
    </div>

    <!-- 筛选控制栏 -->
    <div>
      <h3>全账户列表</h3>
      <el-form :inline="true">
        <el-form-item label="用户">
        <UserSearch v-model="filters.user_id"></UserSearch>
      </el-form-item>


        <el-form-item label="排序">
          <el-select
            v-model="filters.sort_name"
            placeholder="请选择排序字段"
            style="width: 150px"
          >
            <el-option label="综合得分" value="all_score"></el-option>
            <el-option label="综合流动性评分" value="market_liquidity_score"></el-option>
            <el-option label="盘口流动性评分" value="bid_ask_1_score"></el-option>
            <el-option label="Maker成交评分" value="maker_deal_score"></el-option>
            <el-option label="十档挂单评分" value="bid_ask_10_score"></el-option>
            <el-option label="反向合约附加" value="inverse_contract_score" v-if="market_type === 'perpetual'"></el-option>
            <el-option label="USDC合约附加" value="usdc_contract_score" v-if="market_type === 'perpetual'"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="排序">
          <el-select
            v-model="filters.sort_direction"
            placeholder="请选择排序方式"
            style="width: 120px"
          >
            <el-option label="升序" value="asc"></el-option>
            <el-option label="降序" value="desc"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="做市商身份">
          <el-select
            v-model="filters.user_category"
            placeholder="请选择"
            style="width: 150px"
          >
            <el-option label="全部" value="all"></el-option>
            <el-option label="外部做市商" value="outer"></el-option>
            <el-option label="内部做市商" value="inner"></el-option>
            <el-option label="特殊账户" value="special"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
            <el-button type="primary" @click="getData()"
              >查询</el-button
            >
          </el-form-item>
        <el-form-item>
          <el-button 
            type="primary" 
            icon="el-icon-download"
            @click="downloadData"
            :loading="downloadLoading"
          >
            下载
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 数据表格 -->
    <div>
      <el-table 
        :data="tableData" 
        v-loading="loading" 
        stripe
        border
        style="width: 100%;"
      >
        <!-- 站内ID -->
        <el-table-column 
          prop="user_id" 
          label="站内ID" 
        >
        <template slot-scope="scope">
          <el-link type="primary"
                   :href="`/statistics/account-score-user-detail?user_id=${scope.row.user_id}&market_type=${market_type}&report_time=${scope.row.report_time}`"
                   :underline="false"
                   target="_blank">
            {{ scope.row.user_id }}
          </el-link>
        </template>
        </el-table-column>

        <!-- 标识&备注 -->
        <el-table-column 
          prop="remark" 
          label="标识&备注" 
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            {{ scope.row.remark || '--' }}
          </template>
        </el-table-column>

        <!-- 综合流动性评分 -->
        <el-table-column 
          prop="market_liquidity_score" 
          label="综合流动性评分 (30)" 
          
        >
        </el-table-column>

        <!-- 盘口流动性评分 -->
        <el-table-column 
          prop="bid_ask_1_score" 
          label="盘口流动性评分 (20)" 
          
        >
        </el-table-column>

        <!-- Maker成交评分 -->
        <el-table-column 
          prop="maker_deal_score" 
          label="Maker成交评分 (30)" 
          
        >
        </el-table-column>

        <!-- 十档挂单评分 -->
        <el-table-column 
          prop="bid_ask_10_score" 
          label="十档挂单评分 (20)" 
          
        >
        </el-table-column>

        <!-- 反向合约附加 (仅合约市场显示) -->
        <el-table-column 
          v-if="market_type === 'perpetual'"
          prop="inverse_contract_score" 
          label="反向合约附加 (10)" 
          
        >
        </el-table-column>

        <!-- USDC合约附加 (仅合约市场显示) -->
        <el-table-column 
          v-if="market_type === 'perpetual'"
          prop="usdc_contract_score" 
          label="USDC合约附加 (10)" 
          
        >
        </el-table-column>

        <!-- 综合得分 -->
        <el-table-column 
          prop="all_score" 
          label="综合得分" 
          
        >
        </el-table-column>

        <!-- 操作 -->
        <el-table-column 
          label="操作" 
        >
          <template slot-scope="scope">
            <el-tooltip
              content="编辑"
              placement="top"
              :open-delay="500"
              :hide-after="2000"
            >
              <el-button
                size="mini"
                type="primary"
                icon="el-icon-edit"
                circle
                @click="handleEdit(scope.row)"
              ></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

    </div>

    
    <!-- 编辑对话框 -->
    <el-dialog 
      title="编辑ID备注" 
      :visible.sync="editDialog.visible" 
      width="500px"
    >
      <el-form :model="editDialog.form" label-width="100px">
        <el-form-item label="站内ID">
          <el-input v-model="editDialog.form.user_id" disabled></el-input>
        </el-form-item>
        <el-form-item label="标识&备注">
          <el-input
            v-model="editDialog.form.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入标识或备注"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="editDialog.visible = false">取 消</el-button>
        <el-button type="primary" @click="handleSaveEdit">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import moment from 'moment'
import UserSearch from "../../components/user/UserSearch";


export default {
  name: 'MarketMakerMonitor',
  components: { UserSearch },
  data() {
    return {
      title: '',
      market_type: null,
      loading: false,
      downloadLoading: false,
      updateTime: '',
      tableData: [],
      total: 0,
      filters: {
        user_id: null,
        sort_name: 'all_score',
        sort_direction: 'desc',
        user_category: 'all',
        market_type: null,
      },
      editDialog: {
        visible: false,
        form: {
          user_id: '',
          remark: ''
        }
      }
    }
  },
  created() {
    const market_type = this.$route.query.market_type
    this.title = market_type === 'spot' ? '现货' : '合约'
    this.filters.market_type = market_type
    this.market_type = market_type
  },
  watch: {
    '$route'(to, from) {
      const to_market_type = to.query['market_type']
      const from_market_type = from.query['market_type']
      if (to_market_type !== from_market_type) {
        location.reload();
      }
    }
  },
  methods: {
    // 获取数据
    async getData() {
      this.loading = true
      try {
        const response = await this.$axios.get('/api/statistic/real-time/account-score-statistic', {
          params: this.filters
        })
        
        if (response.data.code === 0) {
          const data = response.data.data
          this.tableData = data.data
          this.updateTime = data.updated_at
        } else {
          this.$message.error(`获取数据失败: ${response.data.message}`)
        }
      } catch (error) {
        this.$message.error(`请求失败: ${error.message}`)
      } finally {
        this.loading = false
      }
    },

    // 下载数据
    async downloadData() {
      this.downloadLoading = true
      try {
        const response = await this.$axios.get('/api/statistic/real-time/account-score-statistic', {
          params: {
            ...this.filters,
            export: true
          },
          responseType: 'blob'
        })
        
        const url = window.URL.createObjectURL(new Blob([response.data]))
        const link = document.createElement('a')
        link.href = url
        link.download = `${this.title}-market-maker-monitor-${moment().format('YYYY-MM-DD')}.xlsx`
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        window.URL.revokeObjectURL(url)
        
        this.$message.success('下载成功')
      } catch (error) {
        this.$message.error(`下载失败: ${error.message}`)
      } finally {
        this.downloadLoading = false
      }
    },

    // 编辑
    handleEdit(row) {
      this.editDialog.form = {
        user_id: row.user_id,
        remark: row.remark || ''
      }
      this.editDialog.visible = true
    },

    // 保存编辑
    async handleSaveEdit() {
      try {
        const response = await this.$axios.put('/api/statistic/real-time/account-statistic-remark', {
          user_id: this.editDialog.form.user_id,
          remark: this.editDialog.form.remark,
          market_type: this.market_type
        })
        
        if (response.data.code === 0) {
          this.$message.success('保存成功')
          this.editDialog.visible = false
          this.getData() // 刷新数据
        } else {
          this.$message.error(`保存失败: ${response.data.message}`)
        }
      } catch (error) {
        this.$message.error(`保存失败: ${error.message}`)
      }
    },

    // 格式化日期
    format_date(timestamp) {
      if (!timestamp) return '-'
      return moment(timestamp * 1000).format('YYYY-MM-DD HH:mm:ss')
    }
  },
  
  mounted() {
    this.getData()
  }
}
</script>

<style scoped>
.market-maker-monitor {
  padding: 20px;
}

.update-time {
  color: #666;
  font-size: 14px;
  margin-bottom: 20px;
}

.filter-section {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f5f5f5;
  border-radius: 4px;
}

.filter-section h3 {
  margin: 0 0 15px 0;
  color: #333;
  font-size: 16px;
}

.filter-form {
  margin: 0;
}

.table-section {
  margin-bottom: 20px;
}

.notes {
  margin-top: 20px;
  padding: 15px;
  background-color: #f9f9f9;
  border-radius: 4px;
}

.notes ol {
  margin: 0;
  padding-left: 20px;
}

.notes li {
  margin-bottom: 8px;
  color: #666;
  font-size: 14px;
  line-height: 1.5;
}

/* 表格样式优化 */
.el-table th {
  background-color: #fafafa;
  color: #333;
  font-weight: bold;
}

.el-table td {
  padding: 8px 0;
}

/* 评分列样式 */
.el-table .score-column {
  text-align: center;
  font-weight: bold;
}

/* 操作按钮样式 */
.el-button--mini.is-circle {
  padding: 5px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .filter-form .el-form-item {
    margin-bottom: 10px;
  }
  
  .filter-form .el-input,
  .filter-form .el-select {
    width: 120px !important;
  }
}
</style> 