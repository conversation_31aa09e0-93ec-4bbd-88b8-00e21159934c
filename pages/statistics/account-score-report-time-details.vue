<template>
  <div class="account-score-details">
    <h2 style="font-family: 'Microsoft YaHei', Arial, sans-serif">
      {{ title }} 做市商账户详情
    </h2>
    
    <!-- 更新时间显示 -->
    <div class="update-time">
      更新时间: {{ formatDate(updateTime) }}
    </div>

    <!-- 控制栏 -->
    <div class="control-bar">
      <el-form :inline="true">
        <el-form-item label="切换日期">
          <el-date-picker
            v-model="filters.report_time"
            type="date"
            placeholder="选择日期"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            :picker-options="pickerOptions"
            @change="handleDateChange"
            style="width: 160px"
            :clearable = "false"
          >
          </el-date-picker>
        </el-form-item>
        
        <el-form-item>
          <el-button 
            type="primary" 
            icon="el-icon-download"
            @click="downloadData"
            :loading="downloadLoading"
          >
            下载
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 评分详情表格 -->
    <div class="score-details">
      <h3>评分详情</h3>
      <el-table 
        :data="tableData" 
        v-loading="loading" 
        stripe
        border
        :row-class-name="getRowClassName"
        :row-style="getRowStyle"
        style="width: 100%"
      >
        <!-- 交易市场 -->
        <el-table-column 
          prop="market" 
          label="交易市场" 
          :formatter="formatMarketName"
        >
        </el-table-column>

        <!-- 综合流动性总量 -->
        <el-table-column 
          prop="market_liquidity_amount" 
          label="综合流动性总量" 
          
        >
        </el-table-column>

        <!-- 综合流动性评分 -->
        <el-table-column 
          prop="market_liquidity_score" 
          label="综合流动性评分（30）" 
          
        >
        </el-table-column>

        <!-- 盘口流动性总量 -->
        <el-table-column 
          prop="bid_ask_1_amount" 
          label="盘口流动性总量" 
          
        >
        </el-table-column>

        <!-- 盘口流动性评分 -->
        <el-table-column 
          prop="bid_ask_1_score" 
          label="盘口流动性评分（20）" 
          
        >
        </el-table-column>

        <!-- Maker成交占比 -->
        <el-table-column 
          prop="maker_deal_percent" 
          label="Maker成交占比" 
         
        >
        </el-table-column>

        <!-- Maker成交评分 -->
        <el-table-column 
          prop="maker_deal_score" 
          label="Maker成交评分（30）" 
          
        >
        </el-table-column>

        <!-- 十档挂单情况 -->
        <el-table-column 
          prop="bid_ask_10_count" 
          label="十档挂单情况" 
        >
        </el-table-column>

        <!-- 十档挂单评分 -->
        <el-table-column 
          prop="bid_ask_10_score" 
          label="十档挂单评分（20）" 
          
        >
        </el-table-column>

        <!-- 综合得分 -->
        <el-table-column 
          prop="all_score" 
          label="综合得分" 
          
        >
        </el-table-column>
      </el-table>
    </div>

  </div>
</template>

<script>
import moment from 'moment'

export default {
  name: 'AccountScoreReportTimeDetails',
  data() {
    return {
      title: '',
      market_type: null,
      user_id: null,
      loading: false,
      downloadLoading: false,
      filters: {
        report_time: null,
        user_id: null,
        market_type: null
      },
      updateTime: '',
      tableData: [],
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now() || 
                 time.getTime() < Date.now() - 90 * 24 * 60 * 60 * 1000
        }
      }
    }
  },
  methods: {
    // 获取数据
    async getData() {
      this.loading = true
      try {
        const response = await this.$axios.get('/api/statistic/real-time/account-statistic-market-detail', {
          params: {
            ...this.filters
          }
        })
        
        if (response.data.code === 0) {
          const data = response.data.data
          this.tableData = this.formatTableData(data.items)
          this.updateTime = data.updated_at
          
          // 数据加载完成后应用样式
          this.$nextTick(() => {
            this.applyTableStyles()
          })
        } else {
          this.$message.error(`获取数据失败: ${response.data.message}`)
        }
      } catch (error) {
        this.$message.error(`请求失败: ${error.message}`)
      } finally {
        this.loading = false
      }
    },

    // 格式化表格数据
    formatTableData(items) {
      const tableRows = []
      
      items.forEach(category => {
        // 添加汇总行
        const summaryRow = {
          ...category,
          is_summary: true,
          indent_level: 0
        }
        tableRows.push(summaryRow)
        
        // 添加子市场行
        if (category.sub_markets) {
          category.sub_markets.forEach(subMarket => {
            const subRow = {
              ...subMarket,
              is_summary: false,
              indent_level: 1,
              parent_category: category.market_name
            }
            tableRows.push(subRow)
          })
        }
      })
      
      return tableRows
    },
    // 格式化日期
    formatDate(timestamp) {
      if (!timestamp) return '-'
      return moment(timestamp * 1000).format('YYYY-MM-DD HH:mm:ss')
    },
    // 日期变化处理
    handleDateChange() {
      this.getData()
    },

    // 下载数据
    async downloadData() {
      this.downloadLoading = true
      try {
        const response = await this.$axios.get('/api/statistic/real-time/account-statistic-market-detail', {
          params: {
            ...this.filters,
            export: true
          },
          responseType: 'blob'
        })
        
        const url = window.URL.createObjectURL(new Blob([response.data]))
        const link = document.createElement('a')
        link.href = url
        link.download = `account-score-report-time-details-${this.filters.report_time}.xlsx`
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        window.URL.revokeObjectURL(url)
        
        this.$message.success('下载成功')
      } catch (error) {
        this.$message.error(`下载失败: ${error.message}`)
      } finally {
        this.downloadLoading = false
      }
    },

    // 格式化市场名称
    formatMarketName(row, column, cellValue) {
      if (row.is_summary) {
        return cellValue
      } else {
        return `  ${cellValue}` // 子市场缩进显示
      }
    },

    // 设置行样式
    getRowClassName({ row }) {
      if (row.is_summary) {
        return 'summary-row'
      } else {
        return 'sub-market-row'
      }
    },

    // 设置行内样式
    getRowStyle({ row }) {
      if (row.is_summary) {
        return {
          backgroundColor: '#e6f3ff'
        }
      } else {
        return {
          backgroundColor: '#fafafa'
        }
      }
    },

    // 强制应用表格样式
    applyTableStyles() {
      const table = this.$el.querySelector('.el-table')
      if (table) {
        const summaryRows = table.querySelectorAll('tbody tr.summary-row')
        const subMarketRows = table.querySelectorAll('tbody tr.sub-market-row')
        
        summaryRows.forEach(row => {
          row.style.backgroundColor = '#e6f3ff'
          row.style.fontWeight = 'bold'
          const cells = row.querySelectorAll('td')
          cells.forEach(cell => {
            cell.style.backgroundColor = '#e6f3ff'
            cell.style.fontWeight = 'bold'
          })
        })
        
        subMarketRows.forEach(row => {
          row.style.backgroundColor = '#fafafa'
          const cells = row.querySelectorAll('td')
          cells.forEach(cell => {
            cell.style.backgroundColor = '#fafafa'
          })
        })
      }
    }
  },
  created() {
    const market_type = this.$route.query.market_type
    const user_id = this.$route.query.user_id
    const report_time = this.$route.query.report_time
    this.title = market_type === 'spot' ? '现货' : '合约'
    this.market_type = market_type
    this.user_id = user_id
    this.filters.user_id = user_id
    this.filters.market_type = market_type
    this.filters.report_time = moment(report_time * 1000).format('YYYY-MM-DD')
    },
  mounted() {
    this.getData()
    // 强制应用样式
    this.$nextTick(() => {
      this.applyTableStyles()
    })
  }
}
</script>

<style scoped>
.account-score-details {
  padding: 20px;
}

.update-time {
  color: #666;
  font-size: 14px;
  margin-bottom: 20px;
}

.control-bar {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f5f5f5;
  border-radius: 4px;
}

.score-details h3 {
  margin-bottom: 15px;
  color: #333;
}

.notes {
  margin-top: 20px;
  padding: 15px;
  background-color: #f9f9f9;
  border-radius: 4px;
}

.notes ol {
  margin: 0;
  padding-left: 20px;
}

.notes li {
  margin-bottom: 8px;
  color: #666;
  font-size: 14px;
  line-height: 1.5;
}

/* 表格行样式 - 使用更强的作用域选择器覆盖 Element UI 样式 */
.account-score-details .el-table tbody tr.summary-row {
  background-color: #e6f3ff !important;
}

.account-score-details .el-table tbody tr.summary-row td {
  background-color: #e6f3ff !important;
  font-weight: bold !important;
}

.account-score-details .el-table tbody tr.summary-row .cell {
  background-color: #e6f3ff !important;
  font-weight: bold !important;
}

.account-score-details .el-table tbody tr.sub-market-row {
  background-color: #fafafa !important;
}

.account-score-details .el-table tbody tr.sub-market-row td {
  background-color: #fafafa !important;
}

.account-score-details .el-table tbody tr.sub-market-row .cell {
  background-color: #fafafa !important;
}

.account-score-details .el-table tbody tr.sub-market-row:hover {
  background-color: #f0f0f0 !important;
}

.account-score-details .el-table tbody tr.sub-market-row:hover td {
  background-color: #f0f0f0 !important;
}

.account-score-details .el-table tbody tr.sub-market-row:hover .cell {
  background-color: #f0f0f0 !important;
}

/* 使用更深层的作用域选择器 */
.account-score-details .el-table tbody tr.summary-row > td {
  background-color: #e6f3ff !important;
  font-weight: bold !important;
}

.account-score-details .el-table tbody tr.sub-market-row > td {
  background-color: #fafafa !important;
}

.account-score-details .el-table tbody tr.sub-market-row:hover > td {
  background-color: #f0f0f0 !important;
}

/* 确保子市场行的缩进正确显示 */
.account-score-details .el-table .cell {
  white-space: pre;
}
</style> 