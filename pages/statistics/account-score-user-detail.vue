<template>
  <div class="account-score-user-detail">
    <h2 style="font-family: 'Microsoft YaHei', Arial, sans-serif">
      {{ title }}做市商账户详情
    </h2>
    
    <!-- 更新时间显示 -->
    <div class="update-time">
      更新时间: {{ formatDate(updateTime) }}
    </div>

    <!-- 下载按钮 -->
      <el-button 
        type="primary" 
        icon="el-icon-download"
        @click="downloadData"
        :loading="downloadLoading"
      >
        下载
      </el-button>

    <!-- 数据表格 -->

      <FixedTable
        :data="items" 
        v-loading="loading" 
        stripe
        border
        style="width: 100%;"
        @header-click="show_series_dialog"
        ref="table"
      >
        <!-- 日期 -->
        <el-table-column 
          prop="report_time" 
          label="日期" 
        >
          <template slot-scope="scope">
            <el-link 
              type="primary"
              :href="`/statistics/account-score-report-time-details?report_time=${scope.row.report_time}&user_id=${user_id}&market_type=${market_type}`"
              :underline="false"
              target="_blank"
            >
              {{ formatDate(scope.row.report_time) }}
            </el-link>
          </template>
        </el-table-column>

        <!-- 账户 -->
        <el-table-column 
          prop="user_id" 
          label="账户" 
        >
          <template slot-scope="scope">
            <el-link 
              type="primary"
              :href="`/users/user-details?id=${scope.row.user_id}`"
              :underline="false"
              target="_blank"
            >
              {{ scope.row.user_id }}
            </el-link>
          </template>
        </el-table-column>

        <!-- 综合流动性评分 -->
        <el-table-column 
          prop="market_liquidity_score" 
          label="综合流动性评分 (30)" 
          
          :render-header="renderHeader"
          column-key="综合流动性评分 (30)：综合流动性评分"
        >
        </el-table-column>

        <!-- 盘口流动性评分 -->
        <el-table-column 
          prop="bid_ask_1_score" 
          label="盘口流动性评分 (20)" 
          
          :render-header="renderHeader"
          column-key="盘口流动性评分 (20)：盘口流动性评分"
        >
        </el-table-column>

        <!-- Maker成交评分 -->
        <el-table-column 
          prop="maker_deal_score" 
          label="Maker成交评分 (30)" 
          
          :render-header="renderHeader"
          column-key="Maker成交评分 (30)：Maker成交评分"
        >
        </el-table-column>

        <!-- 十档挂单评分 -->
        <el-table-column 
          prop="bid_ask_10_score" 
          label="十档挂单评分 (20)" 
          
          :render-header="renderHeader"
          column-key="十档挂单评分 (20)：十档挂单评分"
        >
        </el-table-column>

        <!-- 反向合约附加 (仅合约市场显示) -->
        <el-table-column 
          v-if="market_type === 'perpetual'"
          prop="inverse_contract_score" 
          label="反向合约附加 (10)" 
          
          :render-header="renderHeader"
          column-key="反向合约附加 (10)：反向合约附加"
        >
        </el-table-column>

        <!-- USDC合约附加 (仅合约市场显示) -->
        <el-table-column 
          v-if="market_type === 'perpetual'"
          prop="usdc_contract_score" 
          label="USDC合约附加 (10)" 
          
          :render-header="renderHeader"
          column-key="USDC合约附加 (10)：USDC合约附加"
        >
        </el-table-column>

        <!-- 综合得分 -->
        <el-table-column 
          prop="all_score" 
          label="综合得分" 
          
          :render-header="renderHeader"
          column-key="综合得分：综合得分"
        >
        </el-table-column>
      </FixedTable>

      <!-- 分页 -->
      <el-pagination
        :current-page.sync="filters.page"
        :page-size.sync="filters.limit"
        :page-sizes="[20, 50, 100]"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :hide-on-single-page="false"
        layout="total, sizes, prev, pager, next, jumper"
        style="margin-top: 20px; text-align: right;"
      >
      </el-pagination>

    <!-- 趋势图对话框 -->
    <el-dialog 
      :visible.sync="show_dialog" 
      width="80%" 
      :before-close="handleClose"
    >
      <Series 
        ref="series" 
        :render_info="render_info" 
        v-if="show_dialog === true"
        @getChildValue="setChildValue"
      ></Series>
    </el-dialog>
  </div>
</template>

<script>
import moment from 'moment'
import Series from '~/components/Series'
import FixedTable from '~/components/FixedTableRender.vue'
import series from '~/plugins/report/series'

const binding_url = "/api/statistic/real-time/account-statistic-time-detail"
const exclude_render_columns = ['report_time', 'user_id']

export default {
  name: 'AccountScoreUserDetail',
  components: {
    Series,
    FixedTable
  },
  mixins: [series],
  data() {
    return {
      title: '',
      market_type: null,
      user_id: null,
      loading: false,
      downloadLoading: false,
      updateTime: '',
      items: [],
      total: 0,
      filters: {
        user_id: null,
        market_type: null,
        page: 1,
        limit: 20
      },
      show_dialog: false,
      render_info: {},
      table: null
    }
  },
  created() {
    const market_type = this.$route.query.market_type
    const user_id = this.$route.query.user_id
    
    this.title = market_type === 'spot' ? '现货' : '合约'
    this.market_type = market_type
    this.user_id = user_id
    this.filters.user_id = user_id
    this.filters.market_type = market_type
  },
  watch: {
    '$route'(to, from) {
      const to_market_type = to.query['market_type']
      const from_market_type = from.query['market_type']
      const to_user_id = to.query['user_id']
      const from_user_id = from.query['user_id']
      
      if (to_market_type !== from_market_type || to_user_id !== from_user_id) {
        location.reload();
      }
    }
  },
  methods: {
    // 获取数据
    async getData() {
      this.loading = true
      try {
        const response = await this.$axios.get(binding_url, {
          params: this.filters
        })
        
        if (response.data.code === 0) {
          const data = response.data.data
          // 为每个对象添加 report_date 字段
          this.items = data.items
          this.total = data.total
          this.updateTime = data.updated_at
        } else {
          this.$message.error(`获取数据失败: ${response.data.message}`)
        }
      } catch (error) {
        this.$message.error(`请求失败: ${error.message}`)
      } finally {
        this.loading = false
      }
    },

    // 分页大小变化
    handleSizeChange() {
      this.filters.page = 1
      this.getData()
    },

    // 当前页变化
    handleCurrentChange() {
      this.getData()
    },

    // 下载数据
    async downloadData() {
      this.downloadLoading = true
      try {
        const response = await this.$axios.get(binding_url, {
          params: {
            ...this.filters,
            export: true
          },
          responseType: 'blob'
        })
        
        const url = window.URL.createObjectURL(new Blob([response.data]))
        const link = document.createElement('a')
        link.href = url
        link.download = `${this.title}-account-score-user-detail-${this.user_id}-${moment().format('YYYY-MM-DD')}.xlsx`
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        window.URL.revokeObjectURL(url)
        
        this.$message.success('下载成功')
      } catch (error) {
        this.$message.error(`下载失败: ${error.message}`)
      } finally {
        this.downloadLoading = false
      }
    },

    // 显示趋势图对话框
    show_series_dialog(column) {

      if (exclude_render_columns.includes(column.property)) {
        return
      }
      let table = this.$refs['table'];
      console.log(table.columns);
      this.show_dialog = true
      this.set_render_info(column, {
        query_params: {
          user_id: this.user_id,
          market_type: this.market_type
        },
        exclude_columns: exclude_render_columns,
        binding_url: binding_url,
        resp_key: 'items',
        report_type_lower: false,
        exclude_columns: exclude_render_columns
      })
    },

    // 处理对话框关闭
    handleClose() {
      this.show_dialog = false
    },

    // 设置子组件值
    setChildValue(value) {
      // 处理子组件传回的值
      return
    },

    // 格式化日期
    formatDate(timestamp) {
      if (!timestamp) return '-'
      return moment(timestamp * 1000).format('YYYY-MM-DD')
    },

    // 获取表格引用
    get_table() {
      return this.$refs['table']
    }
  },
  
  mounted() {
    this.getData()
    // 确保表格引用正确设置
    this.$nextTick(() => {
      this.table = this.$refs['table']
    })
  }
}
</script>

<style scoped>
.account-score-user-detail {
  padding: 20px;
}

.update-time {
  color: #666;
  font-size: 14px;
  margin-bottom: 20px;
}

.download-section {
  margin-bottom: 20px;
  text-align: right;
}

.table-section {
  margin-bottom: 20px;
}

/* 表格样式优化 */
.el-table th {
  background-color: #fafafa;
  color: #333;
  font-weight: bold;
}

.el-table td {
  padding: 8px 0;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .el-table {
    font-size: 12px;
  }
  
  .el-table .cell {
    padding: 5px;
  }
}
</style>
