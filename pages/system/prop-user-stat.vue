<template>
    <div class="table-data">
      <el-container style="height: 100%">
        <el-header style="padding: 0 0px">
          <div>
            <div style="display: flex; align-items: center; justify-content: space-between;">
              <h2 style="font-family: 'Microsoft YaHei', <PERSON><PERSON>, sans-serif">
                对手盘用户统计
              </h2>
            </div>
          </div>
        </el-header>

         <el-form :inline="true">
          <el-form-item label="周期">
            <el-select v-model="filters.period" @change="handle_change">
                <el-option v-for="n in periods" :key="n" :value="n" :label="`最近${n}天`"></el-option>
            </el-select>
          </el-form-item>

         <el-form-item label="开仓次数>=">
            <el-input-number v-model="filters.position_ge" @change="handle_change"></el-input-number>
         </el-form-item>

         <el-form-item label="盈利金额>=">
          <el-input type="number" v-model="filters.win_amount_ge" @change="handle_change"></el-input>
         </el-form-item>

          <el-form-item label="用户搜索">
          <UserSearch v-model="filters.user_id"
                      @change="handle_change"
          ></UserSearch>
        </el-form-item>

          <el-form-item style="margin-bottom: 10px">
            <el-tooltip content="刷新" placement="right" :open-delay="500" :hide-after="2000">
              <el-button icon="el-icon-refresh-left" circle @click="get_data"></el-button>
            </el-tooltip>
          </el-form-item>
        </el-form>

        <el-row style="margin-top: 5px;"> 更新时间 : {{$formatDate(this.update_time)}} </el-row>

        <el-table :data="items" v-loading="loading" stripe @sort-change="handle_sort_change">
          <el-table-column prop="user_id" label="用户ID">
          <template slot-scope="scope">
            <a :href="scope.row.user_id | user_link"
               target="_blank"
               class="buttonText">
              {{ scope.row.user_id }}
            </a>
          </template>
        </el-table-column>

          <el-table-column label="邮箱" prop="email"></el-table-column>
          <el-table-column label="开仓次数" prop="position_count" sortable="custom"></el-table-column>
          <el-table-column label="盈利次数" prop="win_count" sortable="custom"></el-table-column>
          <el-table-column label="胜率" prop="win_rate" sortable="custom" :formatter="row => `${row.win_rate}%`"></el-table-column>
          <el-table-column label="盈利金额(USDT)" prop="win_amount" sortable="custom"></el-table-column>
        </el-table>
  
        <InfinitePagination :page.sync="filters.page"
                            :page-size.sync="filters.limit"
                            :has-next="has_next"
                            @page-change="get_data">
        </InfinitePagination>
  
        <el-backtop></el-backtop>
  
      </el-container>
    </div>
  </template>
  <script>
  import InfinitePagination from "../../components/InfinitePagination.vue";
  import UserSearch from "../../components/user/UserSearch";

  export default {
    components: { InfinitePagination, UserSearch },
    mounted() {
      this.get_data();
    },
    methods: {
      handle_change() {
        this.filters.page = 1;
        this.get_data();
      },
      get_data() {
        this.loading = true;
        this.$axios.get('/api/system/prop-trading-user-stat', { params: this.filters }).then(res => {
          if (res.data.code !== 0) {
            this.$message.error(`code: ${res.data?.code}; message: ${res.data?.message}`);
            return;
          }
          let data = res.data.data;
          this.items = data.items;
          this.periods = data.periods;
          this.has_next = data.has_next;
          this.update_time = data.update_time;
          this.loading = false;
        }).catch(err => {
          this.$message.error(`刷新失败! (${err})`);
        });
      },
      handle_sort_change(column) {
        if (column.order === 'ascending') {
          this.filters.sort = column.prop;
        } else if (column.order === 'descending') {
          this.filters.sort = `-${column.prop}`;
        } else {
            return;
        }
        this.get_data();
      }
    },
    data() {
      return {
        filters: {
          user_id: null,
          period: 1,
          position_ge: null,
          win_amount_ge: null,
          page: 1,
          limit: 50,
          sort: null,
        },
        items: [],
        periods: [],
        update_time: 0,
        hax_next: true,
        loading: false,
      };
    },
  };
  </script>