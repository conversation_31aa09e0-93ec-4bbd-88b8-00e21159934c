<template>
  <div class="table-data">
    <el-row type="flex" justify="space-between" align="middle">
      <el-col :span="23">
        <h2 style="font-family: 'Microsoft YaHei',Arial,sans-serif;">
          常见问题配置
        </h2>
      </el-col>

      <el-col :span="1">
        <el-button type="primary" @click="handleShowCreate">新建</el-button>
      </el-col>
    </el-row>

    <el-table :data="items" v-loading="loading" stripe>
      <el-table-column prop="index" label="排序"></el-table-column>
      <el-table-column prop="name" label="分类"></el-table-column>
      <el-table-column label="是否展示">
          <template v-slot="scope">
            <el-switch v-model="scope.row.is_show"
                       active-color="#13ce66"
                       inactive-color="#ff4949"
                       @change="handleShow(scope.row)">
            </el-switch>
          </template>
      </el-table-column>
      <el-table-column label="操作">
        <template v-slot="scope">
          <el-tooltip content="编辑" placement="right" :open-delay="500">
            <el-button size="mini" type="primary" icon="el-icon-edit" @click="handleShowEdit(scope.row)" circle>
            </el-button>
          </el-tooltip>
          <el-tooltip content="升序" placement="right" :open-delay="500">
            <el-button size="mini" type="warning" icon="el-icon-top" @click="handleMove(scope.row, true)" circle>
            </el-button>
          </el-tooltip>
          <el-tooltip content="降序" placement="right" :open-delay="500">
            <el-button size="mini" type="warning" icon="el-icon-bottom" @click="handleMove(scope.row, false)" circle>
            </el-button>
          </el-tooltip>
          <el-tooltip content="删除" placement="right" :open-delay="500">
            <el-button size="mini" type="danger" icon="el-icon-delete" @click="handleDelete(scope.row)" circle>
            </el-button>
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>

    <el-dialog :title="dialog_data.id === 0? '新建' : '编辑'" :visible.sync="dialog_show" width="90%"
               :before-close="handleDialogClose">
      <el-form :model="dialog_data" ref="submit_data" label-width="100px" :validate-on-rule-change="false">
        <AITranslateTab
          ref="content-translate"
          :langs="langs"
          :contents="dialog_data.contents"
          :translateAttrNames="['name']"
          business="CUSTOMER_CHATBOT"
          :businessId="dialog_data.id ? dialog_data.id.toString() : null"
          :showAsync="false"
          style="margin-left: 100px;">
          <template v-slot="{ lang, content }">
            <el-form-item label="分类" required>
              <el-input v-model="dialog_data.contents[lang].name"></el-input>
            </el-form-item>
          </template>
        </AITranslateTab>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="handleSubmit">提交</el-button>
      </span>
    </el-dialog>

    <el-backtop></el-backtop>
  </div>
</template>

<style scoped>
</style>

<script>
import AITranslateTab from "@/components/AITranslateTab.vue";

const base_url = '/api/operation/self-service';

export default {
  components: {AITranslateTab},
  methods: {
    initDialogData() {
      let lang_list = Object.keys(this.langs);
      let contents = Object.fromEntries(
        lang_list.map((lang) => [
          lang,
          {
            name: '',
          },
        ])
      );
      this.dialog_data = {
        id: 0,
        contents: contents,
      };
    },
    updateDialogContents(contents) {
      for (let key in contents) {
        this.dialog_data.contents[key] = contents[key];
      }
    },
    updateCategories(data) {
      this.langs = data.langs;
      this.items = data.items;
      this.initDialogData();
    },
    getData() {
      this.loading = true;
      this.$axios.get(base_url + '/qa_category').then(
        res => {
          this.loading = false;
          if (res?.data?.code === 0) {
            this.updateCategories(res.data.data);
          } else {
            this.$message.error(`code: ${res.data?.code}; message: ${res.data?.message}`);
          }
        }
      );
    },
    handleShowCreate() {
      this.initDialogData();
      this.dialog_show = true;
    },
    handleShow(row) {
      this.loading = true;
      this.$axios.put(base_url + `/qa_category/${row.id}/show`).then(res => {
        this.loading = false;
        if (res?.data?.code === 0) {
          this.updateCategories(res.data.data);
        } else {
          this.getData();
          this.$message.error(`code: ${res.data?.code}; message: ${res.data?.message}`);
        }
      }).catch(err => {
        this.loading = false;
        this.$message.error(`err: ${err}`);
      });
    },
    handleShowEdit(row) {
      this.loading = true;
      this.$axios.get(base_url + `/qa_category/${row.id}`).then(res => {
        this.loading = false;
        if (res?.data?.code === 0) {
          this.initDialogData();
          const data = res.data.data;
          this.dialog_data.id = data.id;
          this.updateDialogContents(data.names);
          this.dialog_show = true;
        } else {
          this.$message.error(`code: ${res.data?.code}; message: ${res.data?.message}`);
        }
      }).catch(err => {
        this.loading = false;
        this.$message.error(`err: ${err}`);
      });
    },
    handleMove(row, is_up) {
      this.loading = true;
      this.$axios.put(base_url + `/qa_category/${row.id}/move`, {'is_up': is_up}).then(res => {
        this.loading = false;
        if (res?.data?.code === 0) {
          this.updateCategories(res.data.data);
        } else {
          this.getData();
          this.$message.error(`code: ${res.data?.code}; message: ${res.data?.message}`);
        }
      }).catch(err => {
        this.loading = false;
        this.$message.error(`err: ${err}`);
      });
    },
    handleDelete(row) {
      this.$confirm('确认删除？').then(_ => {
        this.loading = true;
        this.$axios.delete(base_url + `/qa_category/${row.id}`).then(res => {
          this.loading = false;
          if (res?.data?.code === 0) {
            this.updateCategories(res.data.data);
          } else {
            this.getData();
            this.$message.error(`code: ${res.data?.code}; message: ${res.data?.message}`);
          }
        }).catch(err => {
          this.loading = false;
          this.$message.error(`err: ${err}`);
        });
      });
    },
    handleSubmit() {
      let url = base_url + `/qa_category/${this.dialog_data.id}`;
      let func = this.$axios.put;
      if (this.dialog_data.id === 0) {
        url = base_url + '/qa_category';
        func = this.$axios.post;
      }
      this.loading = true;
      func(url, {'names': this.dialog_data.contents}).then(res => {
        this.loading = false;
        if (res?.data?.code === 0) {
          this.updateCategories(res.data.data);
        } else {
          this.$message.error(`code: ${res.data?.code}; message: ${res.data?.message}`);
        }
        this.dialog_show = false;
      }).catch(err => {
        this.loading = false;
        this.$message.error(`err: ${err}`);
      });
    },
    handleDialogClose(done) {
      this.$confirm("未提交编辑内容，确认关闭？").then((_) => {
          this.dialog_show = false;
          this.initDialogData();
          done();
        }).catch((_) => {
        });
    },
  },
  mounted() {
    this.getData();
  },
  data() {
    return {
      dialog_show: false,
      langs: {},
      items: [],
      loading: false,
      dialog_data: {
        id: 0,
        contents: {},
      },
    }
  }
}
</script>
