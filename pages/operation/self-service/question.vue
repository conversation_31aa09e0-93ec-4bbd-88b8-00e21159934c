<template>
  <div class="table-data">
    <el-row type="flex" justify="space-between" align="middle">
      <el-col :span="23">
        <h2 style="font-family: 'Microsoft YaHei',Arial,sans-serif;">
          单个问题配置
        </h2>
      </el-col>

      <el-col :span="1">
        <el-button type="primary" @click="handleShowCreate">新建</el-button>
      </el-col>
    </el-row>

    <el-tabs tab-position="left" style="height: 100%;" v-model="category_tab" @tab-click="getData">
      <el-tab-pane v-for="(category_id, category_name) in categories"
                   :label="category_id" :value="category_name"
                   :key="category_name" :name="category_name">
        <el-table :data="items" v-loading="loading" style="height: 100%" stripe>
          <el-table-column prop="index" label="排序"></el-table-column>
          <el-table-column prop="name" label="分类"></el-table-column>
          <el-table-column prop="title" label="标题"></el-table-column>
          <el-table-column label="是否展示">
            <template v-slot="scope">
              <el-switch v-model="scope.row.is_show"
                         active-color="#13ce66"
                         inactive-color="#ff4949"
                         @change="handleShow(scope.row)">
              </el-switch>
            </template>
          </el-table-column>
          <el-table-column label="是否热门">
            <template v-slot="scope">
              <el-switch v-model="scope.row.is_hot"
                         active-color="#13ce66"
                         inactive-color="#ff4949"
                         @change="handleHot(scope.row)">
              </el-switch>
            </template>
          </el-table-column>
          <el-table-column label="操作">
            <template v-slot="scope">
              <el-tooltip content="编辑" placement="right" :open-delay="500">
                <el-button size="mini" type="primary" icon="el-icon-edit" @click="handleShowEdit(scope.row)" circle>
                </el-button>
              </el-tooltip>
              <el-tooltip content="升序" placement="right" :open-delay="500">
                <el-button size="mini" type="warning" icon="el-icon-top" @click="handleMove(scope.row, true)" circle>
                </el-button>
              </el-tooltip>
              <el-tooltip content="降序" placement="right" :open-delay="500">
                <el-button size="mini" type="warning" icon="el-icon-bottom" @click="handleMove(scope.row, false)"
                           circle>
                </el-button>
              </el-tooltip>
              <el-tooltip content="删除" placement="right" :open-delay="500">
                <el-button size="mini" type="danger" icon="el-icon-delete" @click="handleDelete(scope.row)" circle>
                </el-button>
              </el-tooltip>
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>
    </el-tabs>

    <el-dialog :title="dialog_data.id === 0? '内容新建' : '内容编辑'" :visible.sync="dialog_show" width="90%"
               :before-close="handleDialogClose">
      <el-form :model="dialog_data" ref="submit_data" label-width="120px" :validate-on-rule-change="false">
        <el-form-item label="分类">
          <el-select v-model="dialog_data.category_id" :disabled="dialog_data.id !== 0">
            <template v-for="(category_name, category_id) in categories">
              <el-option v-if="category_id !== '0'" :key="category_id" :label="category_name" :value="category_id">
              </el-option>
            </template>
          </el-select>
        </el-form-item>
        <AITranslateTab
          ref="content-translate"
          :langs="langs"
          :contents="dialog_data.contents"
          :translateAttrNames="['title', 'content']"
          business="CUSTOMER_CHATBOT"
          :businessId="dialog_data.id ? dialog_data.id.toString() : dialog_data.id"
          @start="handleTranslateStart"
          @complete="handleTranslateComplete"
          @async-status-update="handleAsyncResultsUpdate">
          <template v-slot="{ lang, content }">
            <el-form-item label="标题" required>
              <el-input v-model="dialog_data.contents[lang].title" :disabled="is_translating"></el-input>
            </el-form-item>
            <el-form-item label="内容" required>
              <el-input type="textarea" :rows="10"
                        v-model="dialog_data.contents[lang].content" :disabled="is_translating"></el-input>
            </el-form-item>
          </template>
        </AITranslateTab>
        <el-form-item label="帮助中心链接1">
          <el-input v-model="dialog_data.help_url_1"></el-input>
        </el-form-item>
        <el-form-item label="帮助中心链接2">
          <el-input v-model="dialog_data.help_url_2"></el-input>
        </el-form-item>
        <el-form-item label="自助服务链接">
          <el-input v-model="dialog_data.self_service_url"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="handleSubmit">发布</el-button>
      </span>
    </el-dialog>

    <el-backtop></el-backtop>
  </div>
</template>

<style scoped>
</style>

<script>
import AITranslateTab from "@/components/AITranslateTab.vue";

const base_url = '/api/operation/self-service';

export default {
  components: {AITranslateTab},
  methods: {
    initDialogData() {
      let lang_list = Object.keys(this.langs);
      let contents = Object.fromEntries(
        lang_list.map((lang) => [
          lang,
          {
            title: '',
            content: '',
          },
        ])
      );
      this.dialog_data = {
        id: 0,
        category_id: null,
        contents: contents,
        help_url_1: '',
        help_url_2: '',
        self_service_url: '',
      };
    },
    updateDialogContents(contents) {
      for (let key in contents) {
        this.dialog_data.contents[key] = contents[key];
      }
    },
    updateQuestions(data) {
      this.langs = data.langs;
      this.categories = data.categories;
      this.items = data.items;
    },
    getData() {
      this.loading = true;
      this.$axios.get(base_url + `/qa_category/${this.category_tab}/question`).then(
        res => {
          this.loading = false;
          if (res?.data?.code === 0) {
            this.updateQuestions(res.data.data);
          } else {
            this.$message.error(`code: ${res.data?.code}; message: ${res.data?.message}`);
          }
        }
      );
    },
    handleShowCreate() {
      this.initDialogData();
      if (this.$refs['content-translate']) {
        this.$refs['content-translate'].clearData();
      }
      if (this.category_tab !== '0') {
        this.dialog_data.category_id = this.category_tab;
      }
      this.dialog_show = true;
    },
    handleShow(row) {
      this.loading = true;
      this.$axios.put(base_url + `/qa_category/${this.category_tab}/question/${row.id}/show`).then(res => {
        this.loading = false;
        if (res?.data?.code === 0) {
          this.updateQuestions(res.data.data);
        } else {
          this.getData();
          this.$message.error(`code: ${res.data?.code}; message: ${res.data?.message}`);
        }
      }).catch(err => {
        this.loading = false;
        this.$message.error(`err: ${err}`);
      });
    },
    handleHot(row) {
      this.loading = true;
      this.$axios.put(base_url + `/qa_category/${this.category_tab}/question/${row.id}/hot`).then(res => {
        this.loading = false;
        if (res?.data?.code === 0) {
          this.updateQuestions(res.data.data);
        } else {
          this.getData();
          this.$message.error(`code: ${res.data?.code}; message: ${res.data?.message}`);
        }
      }).catch(err => {
        this.loading = false;
        this.$message.error(`err: ${err}`);
      });
    },
    async refreshTransfer() {
      await this.$nextTick();
      if (this.$refs['content-translate']) {
        this.$refs['content-translate'].clearData();
        await this.$refs['content-translate'].refreshAsyncResults();
      }
    },
    handleShowEdit(row) {
      this.loading = true;
      this.$axios.get(base_url + `/qa_category/${this.category_tab}/question/${row.id}`).then(res => {
        this.loading = false;
        if (res?.data?.code === 0) {
          this.initDialogData();
          const data = res.data.data;
          this.dialog_data.id = data.id;
          this.dialog_data.category_id = data.category_id.toString();
          this.updateDialogContents(data.contents);
          this.dialog_data.help_url_1 = data.help_url_1;
          this.dialog_data.help_url_2 = data.help_url_2;
          this.dialog_data.self_service_url = data.self_service_url;
          this.refreshTransfer();
          this.dialog_show = true;
        } else {
          this.$message.error(`code: ${res.data?.code}; message: ${res.data?.message}`);
        }
      }).catch(err => {
        this.loading = false;
        this.$message.error(`err: ${err}`);
      });
    },
    handleMove(row, is_up) {
      this.loading = true;
      this.$axios.put(base_url + `/qa_category/${this.category_tab}/question/${row.id}/move`, {'is_up': is_up}).then(res => {
        this.loading = false;
        if (res?.data?.code === 0) {
          this.updateQuestions(res.data.data);
        } else {
          this.getData();
          this.$message.error(`code: ${res.data?.code}; message: ${res.data?.message}`);
        }
      }).catch(err => {
        this.loading = false;
        this.$message.error(`err: ${err}`);
      });
    },
    handleDelete(row) {
      this.$confirm('确认删除？').then(_ => {
        this.loading = true;
        this.$axios.delete(base_url + `/qa_category/${this.category_tab}/question/${row.id}`).then(res => {
          this.loading = false;
          if (res?.data?.code === 0) {
            this.updateQuestions(res.data.data);
          } else {
            this.getData();
            this.$message.error(`code: ${res.data?.code}; message: ${res.data?.message}`);
          }
        }).catch(err => {
          this.loading = false;
          this.$message.error(`err: ${err}`);
        });
      });
    },
    handleSubmit() {
      let url = base_url + `/qa_category/${this.dialog_data.category_id}/question/${this.dialog_data.id}`;
      let func = this.$axios.put;
      if (this.dialog_data.id === 0) {
        url = base_url + `/qa_category/${this.dialog_data.category_id}/question`;
        func = this.$axios.post;
      }
      func(url, {
        'contents': this.dialog_data.contents,
        'help_url_1': this.dialog_data.help_url_1,
        'help_url_2': this.dialog_data.help_url_2,
        'self_service_url': this.dialog_data.self_service_url,
      }).then(res => {
        if (res?.data?.code === 0) {
          const data = res.data.data;
          this.dialog_data.id = data.id;
          const contentTranslate = this.$refs['content-translate'];
          if (contentTranslate && contentTranslate.asyncToBeRun) {
            contentTranslate.translate_async();
          }
          this.dialog_show = false;
        } else {
          this.$message.error(`code: ${res.data?.code}; message: ${res.data?.message}`);
        }
        this.getData();
      }).catch(err => {
        this.$message.error(`err: ${err}`);
      });
    },
    handleDialogClose(done) {
      this.$confirm("未提交编辑内容，确认关闭？").then((_) => {
        this.dialog_show = false;
        done();
      }).catch((_) => {
      });
    },
    handleTranslateStart() {
      this.is_translating = true
    },
    handleTranslateComplete() {
      this.is_translating = false
    },
    handleAsyncResultsUpdate(data, _) {
      data.forEach((item) => {
        if (item.status === 'finished') {
          this.$axios.get(`/api/ai-translation/tasks`, {
            params: {
              business: 'CUSTOMER_CHATBOT',
              business_id: item.business_id,
              task_id: item.task_id,
              with_content: true
            }
          }).then(res => {
            let r = res.data.data;
            let content;
            if (r.length > 0) {
              content = r[0].content;
            }
            if (content) {
              let target = r[0].target;
              this.$set(this.dialog_data.contents[target], 'title', content.title);
              this.$set(this.dialog_data.contents[target], 'content', content.content);
            }
          }).catch(err => {
            this.$message.error(`加载失败! (${err})`);
          });
        }
      });
    },
  },
  mounted() {
    this.getData();
  },
  data() {
    return {
      dialog_show: false,
      langs: {},
      categories: {},
      items: [],
      category_tab: 0,
      loading: false,
      is_translating: false,
      dialog_data: {
        id: 0,
        category_id: null,
        contents: {},
        help_url_1: '',
        help_url_2: '',
        self_service_url: '',
      },
    }
  }
}
</script>
