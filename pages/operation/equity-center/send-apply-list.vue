<template>
  <el-container>
    <el-main>
      <el-row type="flex" justify="space-between" align="middle">
        <el-col :span="4">
          <h2 style="font-family: 'Microsoft YaHei',Arial,sans-serif;">
            权益发放
          </h2>
        </el-col>
      </el-row>

      <el-form :inline="true" :model="search_data">
        <el-form-item label="发放ID">
          <el-select clearable filterable allow-create v-model="search_data.apply_id" placeholder="<ALL>"
            @change="get_data">
            <el-option v-for="(k, v) in this.apply_title_dict" :key="v" :label="k" :value="v"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="发放标题">
          <el-input placeholder="请输入" clearable v-model="search_data.title" @change="get_data"
            style="width: 300px;"></el-input>
        </el-form-item>

        <el-form-item label="权益类型">
          <el-select clearable filterable v-model="search_data.equity_type" placeholder="<ALL>" @change="get_data">
            <el-option v-for="(k, v) in this.equity_type_dict" :key="v" :label="k" :value="v"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="状态">
          <el-select clearable filterable v-model="search_data.status" placeholder="<ALL>" @change="get_data">
            <el-option v-for="(k, v) in this.status_dict" :key="v" :label="k" :value="v"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="权益ID">
          <el-input placeholder="" clearable v-model="search_data.equity_id" @change="get_data"
            style="width: 200px;"></el-input>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="get_data">搜索</el-button>
        </el-form-item>

        <el-form-item>
          <el-button @click="clear_search">重置</el-button>
        </el-form-item>

        <el-form-item>
          <el-tooltip content="新建权益发放申请" placement="right" :open-delay="500" :hide-after="2000">
            <el-button type="primary" @click="handleCreate">+ 新建发放申请</el-button>
          </el-tooltip>
        </el-form-item>
      </el-form>

      <el-table :data="items" v-loading="loading" style="width: 100%">

        <el-table-column prop="id" label="发放ID"> </el-table-column>

        <el-table-column prop="send_type" label="发放类型" :formatter="(row) => `${send_type_dict[row.send_type]}`">
        </el-table-column>

        <el-table-column prop="title" label="发放标题"> </el-table-column>

        <el-table-column prop="equity_desc" label="发放奖励" width="200px">
          <template slot-scope="scope">
            <el-link v-if="scope.row.equity_type === 'CASHBACK'"
              :href="'/operation/equity-center/cashback/equity-list?equity_id=' + scope.row.equity_id" type="primary"
              target="_blank" :underline="false" style="
                  width: 100%;
                  font-weight: normal;
                  display: inline-block;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  white-space: nowrap;
                ">
              {{ scope.row.equity_desc }}
            </el-link>
            <span v-else>{{ scope.row.equity_desc }}</span>
          </template>
        </el-table-column>


        <el-table-column prop="total_send_count" label="发放数量"> </el-table-column>


        <el-table-column prop="send_at" label="发放时间（UTC+8）" :formatter="(row) => $formatDate(row.send_at)">
        </el-table-column>

        <el-table-column prop="status" label="状态" :formatter="(row) => `${status_dict[row.status]}`"> </el-table-column>

        <el-table-column label="创建人" show-overflow-tooltip>
          <template slot-scope="scope">
            <el-link :href="'/users/user-details?id=' + scope.row.creator" type="primary" target="_blank"
              :underline="false"
              style="width: 100%; font-weight: normal; display: inline-block; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">{{
                scope.row.creator_name || scope.row.creator }}</el-link>
          </template>
        </el-table-column>

        <el-table-column prop="updated_at" label="更新时间（UTC+8）" :formatter="(row) => $formatDate(row.updated_at)">
        </el-table-column>

        <el-table-column label="操作">
          <template slot-scope="scope">
            <span>
              <el-tooltip :content="['CREATED', 'REJECTED'].indexOf(scope.row.status) == -1
                ? '查看'
                : '编辑'
                ">
                <el-button size="mini" @click="handleEdit(scope.row)" type="text">
                  {{
                    ["CREATED", "REJECTED"].indexOf(scope.row.status) == -1
                      ? "查看"
                      : "编辑"
                  }}
                </el-button>
              </el-tooltip>
            </span>
            <span>
              <el-tooltip content="修改状态">
                <el-button size="mini" @click="handleWebAuthn(() => { handleAudited(scope.row) })" type="text"
                  v-if="scope.row.status == 'CREATED'">
                  审核
                </el-button>
                <el-button size="mini" @click="handleForbid(scope.row)" type="text" v-if="scope.row.status == 'PASSED'">
                  禁用
                </el-button>
              </el-tooltip>
            </span>
            <span>
              <el-tooltip content="拒绝">
                <el-button size="mini" @click="handleChangeStatus(scope.row, 'REJECTED')" type="text"
                  v-if="scope.row.status == 'CREATED'">
                  拒绝
                </el-button>
              </el-tooltip>
            </span>
          </template>
        </el-table-column>
      </el-table>

      <el-pagination :current-page.sync="search_data.page" :page-size.sync="search_data.limit"
        @size-change="handle_limit_change" @current-change="handle_page_change" :page-sizes="[50, 100, 200]"
        :hide-on-single-page="false" layout="total, sizes, prev, pager, next, jumper" :total="total">
      </el-pagination>

      <el-dialog :title="action === DIALOG_CREATION ? '添加权益发放申请' : '编辑权益发放申请'
        " :visible.sync="edit_show" width="90%" :before-close="handleClose">

        <el-form :model="submit_data" ref="submit_data" :rules="rules" label-width="150px" :disabled="this.disabled"
          v-loading="loading">
          <el-form-item label="发放类型" required prop="send_type">
            <el-select v-model="submit_data.send_type" style="width: 400px">
              <el-option v-for="(v, k) in send_type_dict" :key="k" :label="v" :value="k">
              </el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="发放标题" required prop="title">
            <el-input placeholder="请输入" v-model="submit_data.title" maxlength="50" style="width: 600px"></el-input>
          </el-form-item>

          <el-form-item label="业务方" required prop="business_party">
            <el-select v-model="submit_data.business_party" style="width: 400px">
              <el-option v-for="(k, v) in this.business_party_dict" :key="v" :label="k" :value="v">
              </el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="发放时间(UTC+8)" required prop="send_at">
            <el-date-picker required v-model="submit_data.send_at" type="datetime" format="yyyy-MM-dd HH:mm:00"
              value-format="timestamp" @change="handleSendAtChange" :picker-options="pickerOptions" placeholder="发放时间"
              style="width: 400px">
            </el-date-picker>
          </el-form-item>

          <el-form-item label="发放权益" required prop="equity_type">
            <el-select v-model="submit_data.equity_type" @change="on_equity_type_change" style="width: 400px">
              <el-option v-for="(k, v) in this.equity_type_dict" :key="v" :label="k" :value="v">
              </el-option>
            </el-select>
            <el-select v-model="submit_data.equity_id" style="width: 400px"
              v-if="submit_data.equity_type === 'CASHBACK'">
              <el-option v-for="k in this.equity_list" :key="k.value" :label="k.label" :value="k.value">
              </el-option>
            </el-select>

            <span v-if="submit_data.equity_type === 'AIRDROP'">
              <el-select v-model="submit_data.airdrop_asset" placeholder="选择币种">
                <el-option label="CET" value="CET"></el-option>
              </el-select>

              <el-input-number v-model="submit_data.airdrop_amount" placeholder="数量" :precision="0"
                :min="0"></el-input-number>

              <span class="unit-text">个</span>
            </span>

          </el-form-item>

          <el-form-item label="客群" required>
            <div>
              <!-- 选择客群方式 -->
              <el-radio-group v-model="submit_data.user_selection_type" @change="handleUserSelectionTypeChange"
                :disabled="disabled">
                <el-radio label="TAG_GROUPS">选择客群</el-radio>
                <el-radio label="MANUAL">输入UID/邮箱</el-radio>
              </el-radio-group>

              <!-- 选择客群 -->
              <div v-if="submit_data.user_selection_type === 'TAG_GROUPS'" key="tag_groups" style="margin-top: 10px;">
                <UserGroupPicker :tagGroups="tagGroups" :dialogUserGroupMap="dialogUserGroupMap"
                  :edit_disabled="disabled">
                </UserGroupPicker>
                <el-form-item :inline="true" v-if="submit_data.id !== '0' || submit_data.id !== undefined">
                  人数统计：{{ submit_data.user_count }}
                  <el-tooltip content="客群下载" placement="right" :open-delay="500" :hide-after="2000">
                    <el-button type="primary" icon="el-icon-download" circle size="mini" style="margin-left: 10px"
                      @click="downloadUser"></el-button>
                  </el-tooltip>
                </el-form-item>
              </div>

              <!-- 输入UID/邮箱 -->
              <div v-if="submit_data.user_selection_type === 'MANUAL'" key="manual_input" style="margin-top: 10px;">
                <el-input type="textarea" v-model="submit_data.manual_user_list"
                  :class="{ 'input-error': manual_input_error }" placeholder="输入UID/邮箱，用英文逗号分隔" :rows="4"
                  style="width: 600px;" :disabled="disabled" @input="handleManualInputChange">
                </el-input>
                <div style="margin-top: 5px; color: #666; font-size: 12px;">
                  支持UID和邮箱混填，多个UID/邮箱需用英文逗号分隔
                </div>
                <el-form-item :inline="true" style="margin-top: 10px;">
                  人数统计：{{ submit_data.group_user_count || manual_user_count }}
                  <el-tooltip content="用户名单下载" placement="right" :open-delay="500" :hide-after="2000">
                    <el-button type="primary" icon="el-icon-download" circle size="mini" style="margin-left: 10px"
                      @click="downloadUser" :disabled="!submit_data.id || submit_data.id === '0'"></el-button>
                  </el-tooltip>
                </el-form-item>
              </div>
            </div>
          </el-form-item>

          <el-form-item label="发放的数量" prop="total_send_count" style="width: 400px">
            <el-input-number placeholder="" :min="1" v-model="submit_data.total_send_count"></el-input-number>
          </el-form-item>

          <el-form-item prop="remark" label="备注">
            <el-input v-model="submit_data.remark" placeholder="备注" maxlength="50" style="width: 600px"></el-input>
          </el-form-item>

          <el-form-item>
            <div style="float: right">
              <el-button @click="handleClose">取 消</el-button>
              <el-button type="primary" @click="submit(true)" v-if="submit_data.status === 'REJECTED'">提交审核</el-button>
              <el-button type="primary" @click="submit(false)" v-else>确 定</el-button>
            </div>
          </el-form-item>

        </el-form>
      </el-dialog>

      <UserTagGroupDialog :tagGroups="tagGroups" :dialogUserGroupMap="dialogUserGroupMap"></UserTagGroupDialog>

      <UserWebAuthn ref="UserWebAuthn" :operation_type="operation_type"></UserWebAuthn>

      <el-backtop></el-backtop>
    </el-main>
  </el-container>
</template>


<script>
import OldUserGroupPicker from "@/components/OldUserGroupPicker";
import UserGroupPicker from "@/components/UserGroupPicker";
import UserTagGroupDialog from "@/components/UserTagGroupDialog";
import UserWebAuthn from "@/components/UserWebAuthn.vue";
import UserGroupFilter from "~/components/UserGroupFilter";
const base_url = "/api/equity-center/send-apply/list";
const detail_url = "/api/equity-center/send-apply";  // + '/' + id

export default {
  components: {
    UserWebAuthn,
    UserGroupFilter,
    UserTagGroupDialog,
    UserGroupPicker,
    OldUserGroupPicker,
  },
  mounted() {
    this.search_data.apply_id = this.$route.query.apply_id;
    this.get_data();
  },
  computed: {
    headers() {
      return {
        'AUTHORIZATION': this.$cookies.get('admin_token'),
        'WebAuthn-Token': this.$refs["UserWebAuthn"] !== undefined ? this.$refs["UserWebAuthn"].webauthn_token : "",
        'Operate-Type': this.operation_type,
      }
    },
    calcTotal() {
      this.tagGroups.forEach(item => { userCount += item.user_count });
    },
  },
  methods: {
    get_data() {
      if (!this.search_data.equity_id) {
        delete this.search_data.equity_id;
      }
      if (!this.search_data.apply_id) {
        delete this.search_data.apply_id;
      }
      this.loading = true;
      this.$axios
        .get(base_url, { params: this.search_data })
        .then((res) => {
          this.loading = false;
          if (res.data.code === 0) {
            let data = res.data.data;
            this.items = data.items;
            this.total = data.total;
            this.equity_type_dict = data.extra.equity_type_dict;
            this.send_type_dict = data.extra.send_type_dict;
            this.status_dict = data.extra.status_dict;
            this.apply_title_dict = data.extra.apply_title_dict;
            this.equity_types = data.extra.equity_types;
            this.business_party_dict = data.extra.business_party_dict;
            this.equity_type_eq_info_dict = data.extra.equity_type_eq_info_dict;
            this.user_selection_type_dict = data.extra.user_selection_type_dict || {};
          } else {
            this.$message.error(
              `code: ${res.data.code}; message: ${res.data.message}`
            );
          }
        });
    },
    reset_page() {
      this.search_data.page = 1;
    },
    handle_limit_change() {
      this.reset_page();
      this.get_data();
    },
    handle_page_change() {
      this.get_data();
    },
    notice(title, message) {
      this.$alert(message, title, {
        confirmButtonText: "确定",
      });
    },
    res_success_notice(res) {
      let title = "提交成功";
      this.notice(title, title);
    },
    res_fail_notice(res) {
      let title = "提交失败";
      let message = `(code: ${res.data.code}; message: ${res.data.message})`;
      this.notice(title, message);
    },
    res_error_notice(error) {
      let title = "提交失败";
      let message = `(code: ${error.response.status}; message: ${error.message})`;
      this.notice(title, message);
    },
    // 调整时间为最近的分钟整点
    adjustToNearestMinute(timestamp) {
      if (!timestamp) return null;
      const date = new Date(timestamp);

      // 如果当前已经是分钟整点，直接返回
      if (date.getSeconds() === 0 && date.getMilliseconds() === 0) {
        return date.getTime();
      }

      // 否则调整到下一个分钟整点
      date.setSeconds(0);
      date.setMilliseconds(0);
      date.setMinutes(date.getMinutes() + 1);
      return date.getTime();
    },

    // 处理开始时间变化
    handleSendAtChange(value) {
      if (!value) return;
      const selectedDate = new Date(value);
      const now = new Date();
      const nextTime = this.adjustToNearestMinute(now);
      // 判断是否是当天
      const isChange = selectedDate.getDate() === now.getDate() &&
        selectedDate.getMonth() === now.getMonth() &&
        selectedDate.getFullYear() === now.getFullYear() && selectedDate < nextTime;

      if (isChange) {
        // 如果是当天，调整为下一个整点
        this.submit_data.send_at = nextTime;
      } else {
        // 如果不是当天，直接使用选择的时间
        this.submit_data.send_at = this.adjustToNearestMinute(value);
      }
    },
    handleCreate() {
      this.edit_show = true;
      this.action = this.DIALOG_CREATION;
      this.disabled = false; // 创建模式下允许编辑
      this.submit_data = {};
      this.$set(this.submit_data, 'equity_type', 'CASHBACK');
      this.$set(this.submit_data, 'send_type', 'DELIVERY');
      this.$set(this.submit_data, 'user_selection_type', 'TAG_GROUPS'); // 默认选择客群
      this.$set(this.submit_data, 'manual_user_list', '');
      this.tagGroups = [];
      this.manual_input_error = false;
      this.manual_user_count = 0;
      this.on_equity_type_change();
    },
    handleEdit(row) {
      this.action = this.DIALOG_EDIT;
      this.disabled =
        ["CREATED", "REJECTED"].indexOf(row.status) == -1 ? true : false;
      this.submit_data = JSON.parse(JSON.stringify(row));
      this.submit_data.id = row.id;
      this.submit_data.send_at = Number(this.submit_data.send_at * 1000);

      this.submit_data.user_selection_type = row.user_selection_type || 'TAG_GROUPS';
      if (this.submit_data.user_selection_type === 'MANUAL') {
        this.$set(this.submit_data, 'manual_user_list', '');
        this.calculateManualUserCount();
        this.tagGroups = [];
      } else {
        this.submit_data.manual_user_list = '';
        this.manual_user_count = 0;
        this.tagGroups = row.tag_groups || [];
      }
      this.manual_input_error = false;

      if (row.equity_type === 'AIRDROP') {
        this.submit_data.airdrop_amount = row.cost_amount;
        this.submit_data.airdrop_asset = row.cost_asset;
      } else {
        this.submit_data.airdrop_amount = null;
        this.submit_data.airdrop_asset = null;
      }
      this.update_eq_by_type(this.submit_data.equity_type);
      this.edit_show = true;
    },
    handleClose() {
      this.$confirm("确认关闭？")
        .then((_) => {
          this.edit_show = false;
          this.disabled = false;
          this.submit_data = {};
          done();
        })
        .catch((_) => { });
    },
    downloadUser() {
      let id = this.submit_data.id;
      if (id === "0") {
        return;
      }
      let url = detail_url + `/${id}/user-download`;
      this.$download_from_url(url, "equity-send-apply-users.xlsx");
    },
    submit(resubmit_wait_audit = false) {
      this.$refs["submit_data"].validate((valid) => {
        if (!valid) {
          this.$alert("校验失败请修改", "校验失败请修改", {
            confirmButtonText: "确定",
          });
          return false;
        } else {
          if (!this.validateManualInput()) {
            return false;
          }

          if (this.submit_data.equity_type !== 'AIRDROP') {
            delete this.submit_data.airdrop_amount;
            delete this.submit_data.airdrop_asset;
          }
          if (this.action === this.DIALOG_CREATION) {
            this.handleCreateConfirm();
          } else {
            this.handleEditConfirm(resubmit_wait_audit);
          }
        }
      });
    },
    handleCreateConfirm() {
      let method = "post";

      // 处理用户选择
      if (this.submit_data.user_selection_type === 'TAG_GROUPS') {
        // 客群选择
        if (this.tagGroups) {
          let groups = [];
          this.tagGroups.forEach((group) => {
            groups.push(group.id);
          });
          this.submit_data.groups = groups;
        } else {
          this.submit_data.groups = [];
        }
        // 清空手动输入数据
        delete this.submit_data.manual_user_list;
      } else {
        // 手动输入用户，直接传递给后端处理
        this.submit_data.groups = [];
      }
      this.loading = true;
      this.$axios[method](base_url, this.submit_data)
        .then((res) => {
          if (res.data.code === 0) {
            this.loading = false;
            this.edit_show = false;
            this.res_success_notice(res);
            this.submit_data = {};
            this.get_data();
          } else {
            this.loading = false;
            this.res_fail_notice(res);
          }
        })
        .catch((e) => {
          this.loading = false;
          console.log(e);
          this.res_error_notice(e);
        });
    },
    handleChangeStatus(row, new_status) {
      this.$axios["patch"](detail_url + `/${row.id}`, { 'status': new_status }).then((res) => {
        if (res.data.code === 0) {
          this.get_data();
          this.$message.success("操作成功");
        } else {
          this.res_fail_notice(res);
        }
      });
    },
    handleForbid(row) {
      this.$confirm("请确认是否禁用此次发放").then((_) => {
        this.$axios["patch"](detail_url + `/${row.id}`, { 'status': 'DISABLED' }).then((res) => {
          if (res.data.code === 0) {
            this.get_data();
            this.$message.success("禁用成功");
          } else {
            this.res_fail_notice(res);
          }
        });
      });
    },
    handleAudited(row) {
      let url = detail_url + `/${row.id}/audit`;
      this.$axios
        .post(url, {}, { headers: this.headers })
        .then((res) => {
          if (res.data.code === 0) {
            this.get_data();
            this.$message.success("成功！");
          } else {
            this.get_data();
            this.$message.error(
              `code: ${res.data.code}; message: ${res.data.message}`
            );
          }
        })
        .catch((err) => {
          this.$message.error(`刷新失败! (${err})`);
        });
    },
    clear_search() {
      this.search_data = {
        page: 1,
        limit: 100,
      };
      this.get_data();
    },
    handleEditConfirm(resubmit_wait_audit) {
      if (this.disabled) {
        this.edit_show = false;
        return;
      }

      // 处理用户选择
      if (this.submit_data.user_selection_type === 'TAG_GROUPS') {
        // 客群选择
        if (this.tagGroups) {
          let groups = [];
          this.tagGroups.forEach((group) => {
            groups.push(group.id);
          });
          this.submit_data.groups = groups;
        } else {
          this.submit_data.groups = [];
        }
        // 清空手动输入数据
        delete this.submit_data.manual_user_list;
      } else {
        // 手动输入用户，直接传递给后端处理
        this.submit_data.groups = [];
      }

      if (!this.submit_data.code) {
        delete this.submit_data.code;
      }
      this.submit_data.resubmit_wait_audit = resubmit_wait_audit;
      let method = "put";
      this.loading = true;
      this.$axios[method](
        detail_url + `/${this.submit_data.id}`,
        this.submit_data
      )
        .then((res) => {
          if (res.data.code === 0) {
            this.loading = false;
            this.edit_show = false;
            this.res_success_notice(res);
            this.submit_data = {};
            this.get_data();
          } else {
            this.loading = false;
            this.res_fail_notice(res);
          }
        })
        .catch((e) => {
          this.loading = false;
          this.res_fail_notice(e);
        });
    },
    update_eq_by_type(equity_type) {
      let eq_info_dict = this.equity_type_eq_info_dict[equity_type];
      if (!eq_info_dict) {
        return;
      }
      this.equity_list = Object.keys(eq_info_dict)
        .sort((a, b) => -b)
        .map(function (key) {
          return { label: eq_info_dict[key], value: key };
        });
    },
    on_equity_type_change() {
      this.update_eq_by_type(this.submit_data.equity_type);
      delete this.submit_data.equity_id;
      if (this.submit_data.equity_type === 'AIRDROP') {
        this.submit_data.airdrop_asset = 'CET';
      }
      this.$forceUpdate();
    },
    async handleWebAuthn(success_callback) {
      const UserWebAuthn = this.$refs["UserWebAuthn"];
      await UserWebAuthn.run();
      await UserWebAuthn.handleWebAuthn().then(() => {
        if (UserWebAuthn.success) {
          this.headers["WebAuthn-Token"] = UserWebAuthn.webauthn_token;
          if (success_callback) {
            success_callback();
          }
          return true;
        } else {
          this.$message.error("WebAuthn校验失败!");
          return false;
        }
      }).catch(err => {
        this.$message.error(`WebAuthn校验失败! ${err}`);
        return false;
      });
    },

    // 处理用户选择类型变化
    handleUserSelectionTypeChange(value) {
      if (value === 'MANUAL') {
        // 切换到手动输入时，清空客群选择
        this.tagGroups = [];
      } else {
        // 切换到客群选择时，清空手动输入
        this.$set(this.submit_data, 'manual_user_list', '');
        this.manual_user_count = 0;
        this.manual_input_error = false;
      }
      // 强制更新视图
      this.$forceUpdate();
    },

    // 处理手动输入变化
    handleManualInputChange() {
      this.manual_input_error = false;
      this.calculateManualUserCount();
    },

    // 计算手动输入的用户数量
    calculateManualUserCount() {
      if (!this.submit_data.manual_user_list.trim()) {
        this.manual_user_count = 0;
        return;
      }

      const users = this.submit_data.manual_user_list
        .split(',')
        .map(user => user.trim())
        .filter(user => user.length > 0);

      const uniqueUsers = [...new Set(users)];
      this.manual_user_count = uniqueUsers.length;
    },

    // 验证手动输入的用户格式
    validateManualInput() {
      if (this.submit_data.user_selection_type !== 'MANUAL') {
        return true;
      }

      if (!this.submit_data.manual_user_list.trim()) {
        this.$message.error('请输入UID/邮箱');
        this.manual_input_error = true;
        return false;
      }

      const users = this.submit_data.manual_user_list
        .split(',')
        .map(user => user.trim())
        .filter(user => user.length > 0);

      // 对用户列表进行去重
      const uniqueUsers = [...new Set(users)];

      // 验证每个用户ID或邮箱格式
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      const uidRegex = /^\d+$/;

      for (let user of uniqueUsers) {
        if (!emailRegex.test(user) && !uidRegex.test(user)) {
          this.$message.error('请检查UID/邮箱填写');
          this.manual_input_error = true;
          return false;
        }
      }

      this.manual_input_error = false;
      return true;
    },


  },
  data() {
    return {
      isOldHistory: false,
      DIALOG_CREATION: "CREATION",
      DIALOG_EDIT: "EDIT",
      loading: false,
      action: null,
      disabled: false,
      edit_show: false,
      total: 0,
      tagGroups: [], // 当前 row 对应的
      dialogUserGroupMap: {
        dialogUserGroup: false,
        dialogUserGroupTotal: 0,
        dialogUserGroupItems: [],
        group_types: {},
        dialogUserGroupLoading: false,
        dialogUserGroupFilters: {
          name: null,
          page: null,
          limit: 10,
        },
      },
      items: [],
      send_type_dict: {},
      equity_type_dict: {},
      status_dict: {},
      equity_list: [],
      equity_type_eq_info_dict: {},
      submit_data: {
        total_send_count: null,
        send_type: 'DELIVERY',
        equity_type: 'CASHBACK',
        equity_id: null,
        airdrop_asset: '',
        airdrop_amount: null,
        business_party: null,
        title: null,
        send_at: null,
        remark: null,
        resubmit_wait_audit: false,
        user_selection_type: 'TAG_GROUPS', // 默认选择客群
        manual_user_list: '', // 手动输入的用户列表
      },
      // 手动输入相关数据
      manual_input_error: false, // 输入错误标识
      manual_user_count: 0, // 手动输入的用户数量
      business_party_dict: {},
      apply_title_dict: {},
      status: {},
      user_selection_type_dict: {}, // 用户选择类型枚举字典
      search_data: {
        apply_id: null,
        equity_type: '',
        page: 1,
        limit: 100,
        title: null,
        status: null,
        equity_id: null,
      },
      pickerOptions: {
        // 限制收货时间不让选择今天以前的
        disabledDate(time) {
          return time.getTime() < Date.now() - 24 * 60 * 60 * 1000;
        },
      },
      operation_type: "EQUITY_SEND_APPLY",
      rules: {
        title: [
          { required: true, message: "请输入发放标题", trigger: "blur" },
          { min: 0, max: 50, message: "最大50个字符", trigger: "blur" },
        ],
        business_party: [
          { required: true, message: "请选择业务方", trigger: "blur" },
        ],
        send_at: [
          { required: true, message: "请选择发送时间", trigger: "blur" },
        ],
      },
    };
  },
};
</script>
