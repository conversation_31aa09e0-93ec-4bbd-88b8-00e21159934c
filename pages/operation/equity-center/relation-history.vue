<template>
  <div class="table-data" id="table-frame">
    <h2 style="font-family: 'Microsoft YaHei',Arial,sans-serif;">
      权益中心相关记录
      <el-tooltip content="刷新" placement="right" :open-delay="500" :hide-after="2000">
        <el-button icon="el-icon-refresh-left" circle @click="rehresh_all"></el-button>
      </el-tooltip>
    </h2>

    <div>
      <el-tabs v-model="active_tab_name" @tab-click="handleTabClick">
        <el-tab-pane
          label="用户空投权益"
          v-loading="loading"
          name="user_airdrop_equity"
          value="user_airdrop_equity"
          @tab-click="get_common_hisotry_data"
        >
          <el-form :inline="true" :model="history_filters">
            <el-form-item label="权益ID">
              <el-input
                v-model="history_filters.equity_id"
                clearable
                @change="get_common_hisotry_data"
              ></el-input>
            </el-form-item>
            <el-form-item label="用户权益ID">
              <el-input
                v-model="history_filters.user_equity_id"
                clearable
                @change="get_common_hisotry_data"
              ></el-input>
            </el-form-item>
            <el-form-item label="用户">
              <UserSearch
                v-model="history_filters.user_id"
                :refresh_method="get_common_hisotry_data"
                @change="get_common_hisotry_data"
              ></UserSearch>
            </el-form-item>
            <el-form-item label="记录ID">
              <el-input
                v-model="history_filters.row_id"
                clearable
                @change="get_common_hisotry_data"
                style="width: 200px;"
              ></el-input>
            </el-form-item>
            <el-form-item label="时间">
              <el-date-picker
                v-model="filters_mid.date_range"
                type="datetimerange"
                range-separator="至"
                start-placeholder="∞"
                end-placeholder="∞"
                @change="handle_date_range_selection"
                style="width: 360px;"
              ></el-date-picker>
            </el-form-item>

            <el-form-item>
              <el-tooltip content="刷新" placement="right" :open-delay="500" :hide-after="2000">
                <el-button icon="el-icon-refresh-left" circle @click="get_common_hisotry_data"></el-button>
              </el-tooltip>
            </el-form-item>
          </el-form>

          <el-table v-loading="loading" :data="user_airdrop_equity_items" stripe>
            <el-table-column prop="id" label="记录ID"></el-table-column>
            <el-table-column label="用户" show-overflow-tooltip>
              <template slot-scope="scope">
                <el-link
                  :href="'/users/user-details?id=' + scope.row.user_id"
                  type="primary"
                  target="_blank"
                  :underline="false"
                  style="width: 100%; font-weight: normal; display: inline-block; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;"
                >{{ scope.row.user_email || scope.row.user_id }}</el-link>
              </template>
            </el-table-column>
            <el-table-column prop="user_equity_id" label="用户权益ID"></el-table-column>
            <el-table-column prop="created_at" label="空投时间">
              <template slot-scope="scope">{{ format_date(scope.row.created_at) }}</template>
            </el-table-column>
            <el-table-column prop="airdrop_amount" label="空投数目">
              <template
                slot-scope="scope"
              >{{ scope.row.airdrop_amount }} {{ scope.row.airdrop_asset }}</template>
            </el-table-column>
            <el-table-column prop="value_amount" label="空投价值">
              <template slot-scope="scope">{{ scope.row.value_amount }} {{ scope.row.value_asset }}</template>
            </el-table-column>
          </el-table>

          <el-pagination
            :current-page.sync="history_filters.page"
            :page-size.sync="history_filters.limit"
            @size-change="get_common_hisotry_data"
            @current-change="get_common_hisotry_data"
            :page-sizes="[50, 100]"
            :total="history_item_total"
            layout="sizes, prev, pager, next"
          ></el-pagination>
        </el-tab-pane>

        <el-tab-pane
          label="用户空投权益-明细"
          v-loading="loading"
          name="user_airdrop_equity_his"
          value="user_airdrop_equity_his"
          @tab-click="get_common_hisotry_data"
        >
          <el-form :inline="true" :model="history_filters">
            <el-form-item label="权益ID">
              <el-input
                v-model="history_filters.equity_id"
                clearable
                @change="get_common_hisotry_data"
              ></el-input>
            </el-form-item>
            <el-form-item label="用户权益ID">
              <el-input
                v-model="history_filters.user_equity_id"
                clearable
                @change="get_common_hisotry_data"
              ></el-input>
            </el-form-item>
            <el-form-item label="用户">
              <UserSearch
                v-model="history_filters.user_id"
                :refresh_method="get_common_hisotry_data"
                @change="get_common_hisotry_data"
              ></UserSearch>
            </el-form-item>
            <el-form-item label="记录ID">
              <el-input
                v-model="history_filters.row_id"
                clearable
                @change="get_common_hisotry_data"
                style="width: 200px;"
              ></el-input>
            </el-form-item>
            <el-form-item label="时间">
              <el-date-picker
                v-model="filters_mid.date_range"
                type="datetimerange"
                range-separator="至"
                start-placeholder="∞"
                end-placeholder="∞"
                @change="handle_date_range_selection"
                style="width: 360px;"
              ></el-date-picker>
            </el-form-item>

            <el-form-item>
              <el-tooltip content="刷新" placement="right" :open-delay="500" :hide-after="2000">
                <el-button icon="el-icon-refresh-left" circle @click="get_common_hisotry_data"></el-button>
              </el-tooltip>
            </el-form-item>
          </el-form>

          <el-table v-loading="loading" :data="user_airdrop_equity_his_items" stripe>
            <el-table-column prop="id" label="记录ID"></el-table-column>
            <el-table-column label="用户" show-overflow-tooltip>
              <template slot-scope="scope">
                <el-link
                  :href="'/users/user-details?id=' + scope.row.user_id"
                  type="primary"
                  target="_blank"
                  :underline="false"
                  style="width: 100%; font-weight: normal; display: inline-block; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;"
                >{{ scope.row.user_email || scope.row.user_id }}</el-link>
              </template>
            </el-table-column>
            <el-table-column prop="user_equity_id" label="用户权益ID"></el-table-column>
            <el-table-column prop="created_at" label="空投时间">
              <template slot-scope="scope">{{ format_date(scope.row.created_at) }}</template>
            </el-table-column>
            <el-table-column prop="finished_at" label="完成时间">
              <template
                slot-scope="scope"
              >{{ scope.row.finished_at? format_date(scope.row.finished_at): '-' }}</template>
            </el-table-column>
            <el-table-column prop="airdrop_amount" label="空投数目">
              <template
                slot-scope="scope"
              >{{ scope.row.airdrop_amount }} {{ scope.row.airdrop_asset }}</template>
            </el-table-column>
            <el-table-column prop="value_amount" label="空投价值">
              <template slot-scope="scope">{{ scope.row.value_amount }} {{ scope.row.value_asset }}</template>
            </el-table-column>
            <el-table-column prop="asset_rates" label="币种汇率">
              <template slot-scope="scope">
                <div>
                  <div
                    v-for="(rate, type) in scope.row.asset_rates"
                    :key="type"
                  >{{ type }}: {{ rate }} USD</div>
                </div>
              </template>
            </el-table-column>
          </el-table>

          <el-pagination
            :current-page.sync="history_filters.page"
            :page-size.sync="history_filters.limit"
            @size-change="get_common_hisotry_data"
            @current-change="get_common_hisotry_data"
            :page-sizes="[50, 100]"
            :total="history_item_total"
            layout="sizes, prev, pager, next"
          ></el-pagination>
        </el-tab-pane>

        <el-tab-pane
          label="用户返现权益-明细记录"
          v-loading="loading"
          name="cashback_equity_his"
          value="cashback_equity_his"
          @tab-click="get_common_hisotry_data"
        >
          <el-form :inline="true" :model="history_filters">
            <el-form-item label="权益ID">
              <el-input
                v-model="history_filters.equity_id"
                clearable
                @change="get_common_hisotry_data"
              ></el-input>
            </el-form-item>
            <el-form-item label="用户权益ID">
              <el-input
                v-model="history_filters.user_equity_id"
                clearable
                @change="get_common_hisotry_data"
              ></el-input>
            </el-form-item>
            <el-form-item label="用户">
              <UserSearch
                v-model="history_filters.user_id"
                :refresh_method="get_common_hisotry_data"
                @change="get_common_hisotry_data"
              ></UserSearch>
            </el-form-item>
            <el-form-item label="记录ID">
              <el-input
                v-model="history_filters.row_id"
                clearable
                @change="get_common_hisotry_data"
                style="width: 200px;"
              ></el-input>
            </el-form-item>
            <el-form-item label="时间">
              <el-date-picker
                v-model="filters_mid.date_range"
                type="datetimerange"
                range-separator="至"
                start-placeholder="∞"
                end-placeholder="∞"
                @change="handle_date_range_selection"
                style="width: 360px;"
              ></el-date-picker>
            </el-form-item>

            <el-form-item>
              <el-tooltip content="刷新" placement="right" :open-delay="500" :hide-after="2000">
                <el-button icon="el-icon-refresh-left" circle @click="get_common_hisotry_data"></el-button>
              </el-tooltip>
            </el-form-item>
          </el-form>

          <el-table v-loading="loading" :data="cashback_equity_his_items" stripe>
            <el-table-column prop="id" label="记录ID" min-width="120px"></el-table-column>
            <el-table-column prop="created_at" label="创建时间" min-width="180px">
              <template slot-scope="scope">{{ format_date(scope.row.created_at) }}</template>
            </el-table-column>
            <el-table-column label="用户" show-overflow-tooltip min-width="180px">
              <template slot-scope="scope">
                <el-link
                  :href="'/users/user-details?id=' + scope.row.user_id"
                  type="primary"
                  target="_blank"
                  :underline="false"
                  style="width: 100%; font-weight: normal; display: inline-block; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;"
                >{{ scope.row.user_email || scope.row.user_id }}</el-link>
              </template>
            </el-table-column>
            <el-table-column prop="user_equity_id" label="用户权益ID" min-width="120px"></el-table-column>
            <el-table-column prop="delta_cost_amount" label="返现额度" min-width="180px">
              <template
                slot-scope="scope"
              >{{ scope.row.delta_cost_amount }} {{ scope.row.cost_asset }}</template>
            </el-table-column>

            <el-table-column prop="delta_cashback_amount" label="返现数目" min-width="180px">
              <template
                slot-scope="scope"
              >{{ scope.row.delta_cashback_amount }} {{ scope.row.cashback_asset }}</template>
            </el-table-column>

            <el-table-column prop="total_used_cost_amount" label="累积返现额度" min-width="180px">
              <template
                slot-scope="scope"
              >{{ scope.row.total_used_cost_amount }} {{ scope.row.cost_asset }}</template>
            </el-table-column>

            <el-table-column prop="total_cashback_amount" label="累积返现数目" min-width="180px">
              <template
                slot-scope="scope"
              >{{ scope.row.total_cashback_amount }} {{ scope.row.cashback_asset }}</template>
            </el-table-column>

            <el-table-column
              prop="created_at"
              label="权益结算时间(UTC+8)"
              min-width="180px"
              :formatter="(row) => $formatDate(row.settle_at)"
            ></el-table-column>
          </el-table>

          <el-pagination
            :current-page.sync="history_filters.page"
            :page-size.sync="history_filters.limit"
            @size-change="get_common_hisotry_data"
            @current-change="get_common_hisotry_data"
            :page-sizes="[50, 100]"
            :total="history_item_total"
            layout="sizes, prev, pager, next"
          ></el-pagination>
        </el-tab-pane>

        <el-tab-pane
          label="用户返现权益-结算记录"
          v-loading="loading"
          name="cashback_settlement_his"
          value="cashback_settlement_his"
          @tab-click="get_common_hisotry_data"
        >
          <el-form :inline="true" :model="history_filters">
            <el-form-item label="用户">
              <UserSearch
                v-model="history_filters.user_id"
                :refresh_method="get_common_hisotry_data"
                @change="get_common_hisotry_data"
              ></UserSearch>
            </el-form-item>
            <el-form-item label="状态">
              <el-select
                v-model="history_filters.settlement_status"
                clearable
                placeholder="<ALL>"
                @change="get_common_hisotry_data"
                style="width: 180px;"
              >
                <el-option
                  v-for="(name, v) in settlement_status_dict"
                  :key="v"
                  :label="name"
                  :value="v"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="记录ID">
              <el-input
                v-model="history_filters.row_id"
                clearable
                @change="get_common_hisotry_data"
                style="width: 200px;"
              ></el-input>
            </el-form-item>
            <el-form-item label="时间">
              <el-date-picker
                v-model="filters_mid.date_range"
                type="datetimerange"
                range-separator="至"
                start-placeholder="∞"
                end-placeholder="∞"
                @change="handle_date_range_selection"
                style="width: 360px;"
              ></el-date-picker>
            </el-form-item>

            <el-form-item>
              <el-tooltip content="刷新" placement="right" :open-delay="500" :hide-after="2000">
                <el-button icon="el-icon-refresh-left" circle @click="get_common_hisotry_data"></el-button>
              </el-tooltip>
            </el-form-item>
          </el-form>

          <el-table v-loading="loading" :data="cashback_settlement_his_items" stripe>
            <el-table-column prop="id" label="记录ID"></el-table-column>
            <el-table-column prop="created_at" label="创建时间">
              <template slot-scope="scope">{{ format_date(scope.row.created_at) }}</template>
            </el-table-column>
            <el-table-column label="用户" show-overflow-tooltip>
              <template slot-scope="scope">
                <el-link
                  :href="'/users/user-details?id=' + scope.row.user_id"
                  type="primary"
                  target="_blank"
                  :underline="false"
                  style="width: 100%; font-weight: normal; display: inline-block; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;"
                >{{ scope.row.user_email || scope.row.user_id }}</el-link>
              </template>
            </el-table-column>
            <el-table-column prop="trade_type" label="交易类型"></el-table-column>
            <el-table-column prop="status" label="状态">
              <template
                slot-scope="scope"
              >{{ settlement_status_dict[scope.row.status] || scope.row.status }}</template>
            </el-table-column>
            <el-table-column prop="settle_time" label="结算时间">
              <template slot-scope="scope">{{ format_date(scope.row.settle_time) }}</template>
            </el-table-column>
            <el-table-column prop="total_cost_amount" label="手续费价值数目">
              <template
                slot-scope="scope"
              >{{ scope.row.total_cost_amount }} {{ scope.row.cost_asset }}</template>
            </el-table-column>
            <el-table-column prop="used_cost_amount" label="已结算数目">
              <template
                slot-scope="scope"
              >{{ scope.row.used_cost_amount }} {{ scope.row.cost_asset }}</template>
            </el-table-column>
            <el-table-column prop="asset_rates" label="币种汇率">
              <template slot-scope="scope">
                <div>
                  <div
                    v-for="(rate, type) in scope.row.asset_rates"
                    :key="type"
                  >{{ type }}: {{ rate }} USD</div>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="fee_his_ids" label="手续费记录ID" show-overflow-tooltip>
              <template slot-scope="scope">{{ scope.row.fee_his_ids.join('、') }}</template>
            </el-table-column>
          </el-table>

          <el-pagination
            :current-page.sync="history_filters.page"
            :page-size.sync="history_filters.limit"
            @size-change="get_common_hisotry_data"
            @current-change="get_common_hisotry_data"
            :page-sizes="[50, 100]"
            :total="history_item_total"
            layout="sizes, prev, pager, next"
          ></el-pagination>
        </el-tab-pane>

        <el-tab-pane
          label="用户返现权益-手续费记录"
          v-loading="loading"
          name="cashback_trade_fee_his"
          value="cashback_trade_fee_his"
          @tab-click="get_common_hisotry_data"
        >
          <el-form :inline="true" :model="history_filters">
            <el-form-item label="用户">
              <UserSearch
                v-model="history_filters.user_id"
                :refresh_method="get_common_hisotry_data"
                @change="get_common_hisotry_data"
              ></UserSearch>
            </el-form-item>
            <el-form-item label="主账户ID">
              <UserSearch
                v-model="history_filters.main_user_id"
                :refresh_method="get_common_hisotry_data"
                @change="get_common_hisotry_data"
              ></UserSearch>
            </el-form-item>
            <el-form-item label="交易记录ID">
              <el-input
                v-model="history_filters.trade_business_id"
                clearable
                placeholder="成交ID、兑换订单ID"
                @change="get_common_hisotry_data"
                style="width: 300px;"
              ></el-input>
            </el-form-item>
            <el-form-item label="结算记录ID">
              <el-input
                v-model="history_filters.settle_his_id"
                clearable
                @change="get_common_hisotry_data"
                style="width: 200px;"
              ></el-input>
            </el-form-item>
            <el-form-item label="记录ID">
              <el-input
                v-model="history_filters.row_id"
                clearable
                @change="get_common_hisotry_data"
                style="width: 200px;"
              ></el-input>
            </el-form-item>

            <el-form-item label="时间">
              <el-date-picker
                v-model="filters_mid.date_range"
                type="datetimerange"
                range-separator="至"
                start-placeholder="∞"
                end-placeholder="∞"
                @change="handle_date_range_selection"
                style="width: 360px;"
              ></el-date-picker>
            </el-form-item>

            <el-form-item>
              <el-tooltip content="刷新" placement="right" :open-delay="500" :hide-after="2000">
                <el-button icon="el-icon-refresh-left" circle @click="get_common_hisotry_data"></el-button>
              </el-tooltip>
            </el-form-item>
          </el-form>

          <el-table v-loading="loading" :data="cashback_trade_fee_his_items" stripe>
            <el-table-column prop="id" label="记录ID"></el-table-column>
            <el-table-column prop="created_at" label="创建时间">
              <template slot-scope="scope">{{ format_date(scope.row.created_at) }}</template>
            </el-table-column>
            <el-table-column label="用户" show-overflow-tooltip>
              <template slot-scope="scope">
                <el-link
                  :href="'/users/user-details?id=' + scope.row.trade_user_id"
                  type="primary"
                  target="_blank"
                  :underline="false"
                  style="width: 100%; font-weight: normal; display: inline-block; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;"
                >{{ scope.row.user_email || scope.row.trade_user_id }}</el-link>
              </template>
            </el-table-column>
            <el-table-column prop="trade_business_id" label="交易记录ID"></el-table-column>
            <el-table-column prop="settle_his_id" label="结算记录ID"></el-table-column>
            <el-table-column prop="trade_type" label="交易类型"></el-table-column>
            <el-table-column prop="trade_time" label="交易时间">
              <template slot-scope="scope">{{ format_date(scope.row.trade_time) }}</template>
            </el-table-column>
            <el-table-column prop="fees" label="手续费" min-width="200px">
              <template slot-scope="scope">
                <div>
                  <div v-for="f in scope.row.fees" :key="f[0]">{{ f }}</div>
                </div>
              </template>
            </el-table-column>
          </el-table>

          <el-pagination
            :current-page.sync="history_filters.page"
            :page-size.sync="history_filters.limit"
            @size-change="get_common_hisotry_data"
            @current-change="get_common_hisotry_data"
            :page-sizes="[50, 100]"
            :total="history_item_total"
            layout="sizes, prev, pager, next"
          ></el-pagination>
        </el-tab-pane>

        <el-tab-pane
          label="用户业务标记"
          v-loading="loading"
          name="user_biz_tag"
          value="user_biz_tag"
          @tab-click="get_common_hisotry_data"
        >
          <el-form :inline="true" :model="history_filters">
            <el-form-item label="用户">
              <UserSearch
                v-model="history_filters.user_id"
                :refresh_method="get_common_hisotry_data"
                @change="get_common_hisotry_data"
              ></UserSearch>
            </el-form-item>

            <el-form-item>
              <el-tooltip content="刷新" placement="right" :open-delay="500" :hide-after="2000">
                <el-button icon="el-icon-refresh-left" circle @click="get_common_hisotry_data"></el-button>
              </el-tooltip>
            </el-form-item>
          </el-form>

          <el-table v-loading="loading" :data="user_biz_tag_items" stripe>
            <el-table-column prop="id" label="记录ID"></el-table-column>
            <el-table-column prop="created_at" label="创建时间">
              <template slot-scope="scope">{{ format_date(scope.row.created_at) }}</template>
            </el-table-column>
            <el-table-column prop="updated_at" label="更新时间">
              <template slot-scope="scope">{{ format_date(scope.row.updated_at) }}</template>
            </el-table-column>
            <el-table-column label="用户" show-overflow-tooltip>
              <template slot-scope="scope">
                <el-link
                  :href="'/users/user-details?id=' + scope.row.user_id"
                  type="primary"
                  target="_blank"
                  :underline="false"
                  style="width: 100%; font-weight: normal; display: inline-block; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;"
                >{{ scope.row.user_email || scope.row.user_id }}</el-link>
              </template>
            </el-table-column>
            <el-table-column prop="biz_tag" label="类型">
              <template slot-scope="scope">{{ biz_tag_dict[scope.row.biz_tag] || scope.row.status }}</template>
            </el-table-column>
            <el-table-column prop="source" label="来源">
              <template
                slot-scope="scope"
              >{{ biz_tag_source_dict[scope.row.source] || scope.row.source }}</template>
            </el-table-column>
            <el-table-column prop="status" label="状态"></el-table-column>
          </el-table>

          <el-pagination
            :current-page.sync="history_filters.page"
            :page-size.sync="history_filters.limit"
            @size-change="get_common_hisotry_data"
            @current-change="get_common_hisotry_data"
            :page-sizes="[50, 100]"
            :total="history_item_total"
            layout="sizes, prev, pager, next"
          ></el-pagination>
        </el-tab-pane>
      </el-tabs>
    </div>

    <el-backtop></el-backtop>
  </div>
</template>

<style scoped>
#sty_params .el-form-item {
  margin-bottom: 5px;
  font-size: 16px;
  color: #000000;
}
</style>

<script>
import moment from "moment";
import UserSearch from "../../../components/user/UserSearch";

const history_url = "/api/equity-center/relation-history";

export default {
  components: { UserSearch },
  methods: {
    cleanUpParams(params, keysToClean) {
      keysToClean.forEach((key) => {
        if (
          params[key] == undefined ||
          params[key] == null ||
          params[key] == ""
        ) {
          delete params[key];
        }
      });
    },
    get_common_hisotry_data() {
      let params = Object.assign({}, this.history_filters);
      let tab_name = this.active_tab_name;
      params.history_type = tab_name.replaceAll("_history", "");
      this.cleanUpParams(params, [
        "row_id",
        "equity_id",
        "user_equity_id",
        "trade_business_id",
        "settle_his_id",
      ]);
      this.loading = true;
      this.$axios.get(`${history_url}`, { params: params }).then((res) => {
        this.loading = false;
        if (res && res.data.code === 0) {
          let data = res.data.data;
          this.history_item_total = data.total;
          this.set_history_items(tab_name, data.items);
          this.settlement_status_dict = data.extra.settlement_status_dict;
          this.biz_tag_dict = data.extra.biz_tag_dict;
          this.biz_tag_source_dict = data.extra.biz_tag_source_dict;
        } else {
          this.history_item_total = 1;
          this.$message.error(
            `code: ${res.data?.code}; message: ${
              res.data?.message
            }; data: ${JSON.stringify(res.data?.data)}`
          );
        }
      });
    },
    set_history_items(tab_name, items) {
      console.log(tab_name, 123);
      if (tab_name == "user_airdrop_equity") {
        this.user_airdrop_equity_items = items;
      } else if (tab_name == "user_airdrop_equity_his") {
        this.user_airdrop_equity_his_items = items;
      } else if (tab_name == "cashback_settlement_his") {
        this.cashback_settlement_his_items = items;
      } else if (tab_name == "cashback_equity_his") {
        this.cashback_equity_his_items = items;
      } else if (tab_name == "cashback_trade_fee_his") {
        this.cashback_trade_fee_his_items = items;
      } else if (tab_name == "user_biz_tag") {
        this.user_biz_tag_items = items;
      }
    },
    format_transfer_direction(row) {
      let s = "";
      if (row.from_account_id === 0) {
        s += "扣减现货账户余额；";
      } else if (row.from_account_id > 0) {
        s += "扣减借贷账户余额；";
      }
      if (row.to_account_id === 0) {
        s += "增加现货账户余额";
      } else if (row.to_account_id > 0) {
        s += "增加借贷账户余额";
      }
      return s;
    },
    handleTabClick(target) {
      this.get_common_hisotry_data();
    },
    rehresh_all() {
      this.reset_page();
      this.get_common_hisotry_data();
    },
    handle_history_refresh() {
      this.get_common_hisotry_data();
    },
    handle_date_range_selection() {
      this.reset_page();
      this.handle_history_refresh();
    },
    handle_limit_change() {
      this.reset_page();
      this.handle_history_refresh();
    },
    handle_page_change() {
      this.handle_history_refresh();
    },
    reset_page() {
      this.history_filters.page = 1;
    },
    handle_content_copy() {
      this.$message.success("内容已复制到剪贴板");
    },
    format_date(timestamp, pattern = "YYYY-MM-DD HH:mm:ss") {
      if (timestamp) {
        return moment(Number(timestamp) * 1000).format(pattern);
      }
      return "-";
    },
  },
  created() {
    let loan_order_id = this.$route.query.loan_order_id;
    if (loan_order_id) {
      this.history_filters.loan_order_id = loan_order_id;
    }
  },
  mounted() {
    this.get_common_hisotry_data();
  },
  data() {
    return {
      active_tab_name: "user_airdrop_equity",
      loan_order_id: null,
      user_airdrop_equity_items: [],
      user_airdrop_equity_his_items: [],
      cashback_settlement_his_items: [],
      cashback_trade_fee_his_items: [],
      user_biz_tag_items: [],
      history_item_total: 0,
      history_filters: {
        history_type: null,
        row_id: null,
        user_id: null,
        equity_id: null,
        main_user_id: null,
        user_equity_id: null,
        trade_business_id: null,
        settle_his_id: null,
        start: null,
        end: null,
        settlement_status: null,
        page: 1,
        limit: 50,
      },
      filters_mid: {
        date_range: null,
      },
      status_dict: {},
      settlement_status_dict: {},
      biz_tag_dict: {},
      biz_tag_source_dict: {},
      loading: true,
    };
  },
  watch: {
    "filters_mid.date_range": function (date_range) {
      Object.assign(this.history_filters, {
        start:
          date_range && date_range[0] ? date_range[0].getTime() / 1000 : null,
        end:
          date_range && date_range[1] ? date_range[1].getTime() / 1000 : null,
      });
    },
  },
};
</script>
