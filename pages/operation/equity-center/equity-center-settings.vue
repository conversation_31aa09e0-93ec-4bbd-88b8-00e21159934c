<template>
  <div class="table-data">
    <el-row type="flex" justify="space-between" align="middle">
      <el-col :span="22">
        <h2 style="font-family: 'Microsoft YaHei', Arial, sans-serif">
          权益中心全局配置
        </h2>
      </el-col>

      <el-col :span="1">
        <el-tooltip
          content="刷新"
          placement="right"
          :open-delay="500"
          :hide-after="2000"
        >
          <el-button
            icon="el-icon-refresh-left"
            circle
            @click="get_data"
          ></el-button>
        </el-tooltip>
      </el-col>
    </el-row>

    <el-table
      :data="items"
      v-loading="loading"
      :span-method="span_method"
      stripe
    >
      <el-table-column label="名称" show-overflow-tooltip>
        <template slot-scope="scope">
          <span> {{ scope.row.desc }} </span>
          <el-tooltip
            v-if="scope.row.meta.remark"
            :content="scope.row.meta.remark"
            placement="right"
          >
            <i class="el-icon-info"></i>
          </el-tooltip>
        </template>
      </el-table-column>

      <el-table-column label="类型" prop="type" show-overflow-tooltip>
      </el-table-column>

      <template
        v-for="[label, prop] in [
          ['默认值', 'default_value'],
          ['当前值', 'value'],
        ]"
      >
        <el-table-column :key="label" :label="label" show-overflow-tooltip>
          <template slot-scope="scope">
            <span v-if="scope.row.type === 'bool'">
              <i
                v-if="scope.row[prop]"
                class="el-icon-success"
                style="color: green"
              ></i>
              <i v-else class="el-icon-error" style="color: red"></i>
            </span>
            <span v-else>
              {{ scope.row[prop] }}
            </span>
          </template>
        </el-table-column>
      </template>

      <el-table-column label="操作">
        <template slot-scope="scope">
          <span v-if="scope.row.editable">
            <el-tooltip
              content="重置"
              placement="left"
              :open-delay="500"
              :hide-after="2000"
            >
              <el-button
                size="mini"
                type="warning"
                icon="el-icon-refresh-left"
                circle
                @click="on_reset_button_clicked(scope.row)"
              ></el-button>
            </el-tooltip>

            <span v-if="scope.row.type === 'bool'">
              <el-tooltip
                v-if="scope.row.value"
                content="关闭"
                placement="right"
                :open-delay="500"
                :hide-after="2000"
              >
                <el-button
                  size="mini"
                  type="danger"
                  icon="el-icon-minus"
                  circle
                  @click="on_toggle_button_clicked(scope.row)"
                ></el-button>
              </el-tooltip>

              <el-tooltip
                v-else
                content="开启"
                placement="right"
                :open-delay="500"
                :hide-after="2000"
              >
                <el-button
                  size="mini"
                  type="success"
                  icon="el-icon-plus"
                  circle
                  @click="on_toggle_button_clicked(scope.row)"
                ></el-button>
              </el-tooltip>
            </span>

            <span v-else>
              <el-tooltip
                content="编辑"
                placement="right"
                :open-delay="500"
                :hide-after="2000"
              >
                <el-button
                  size="mini"
                  type="primary"
                  icon="el-icon-edit"
                  circle
                  @click="on_edit_button_clicked(scope.row)"
                ></el-button>
              </el-tooltip>
            </span>
          </span>
        </template>
      </el-table-column>
    </el-table>

    <el-backtop></el-backtop>
  </div>
</template>

<script>
import moment from "moment";

const bind_url = "/api/equity-center/settings";

export default {
  methods: {
    get_data(silent = false) {
      if (!silent) {
        this.loading = true;
      }
      this.$axios.get(bind_url).then((res) => {
        this.loading = false;
        if (res?.data?.code === 0) {
          this.items = res.data.data.items;
        } else {
          this.items = [];
          this.$message.error(
            `code: ${res.data?.code}; message: ${res.data?.message}`
          );
        }
      });
    },
    refresh_all() {
      this.get_data();
    },
    refresh_all_items(silent = false) {
      this.get_data(silent);
    },
    handle_page_refresh() {
      this.refresh_all_items();
    },
    on_reset_button_clicked(row) {
      this.$confirm(
        `确定重置 <b>${row.desc}</b> (${row.name})?<br>默认值: ${row.default_value}`,
        "",
        {
          type: "warning",
          inputValue: row.value,
          dangerouslyUseHTMLString: true,
        }
      )
        .then(() => {
          this.$axios
            .delete(`${bind_url}/${row.name}`)
            .then((res) => {
              if (res?.data?.code === 0) {
                let data = res.data.data;
                row.value = data.value;
                this.$message.success("重置成功!");
                this.refresh_all_items(true, row.category);
              } else {
                this.$message.error(
                  `重置失败! (code: ${res.data?.code}; message: ${res.data?.message})`
                );
              }
            })
            .catch((err) => {
              this.$message.error(`重置失败! (${err})`);
            });
        })
        .catch(() => {});
    },
    on_edit_button_clicked(row) {
      this.$prompt(
        `编辑 <b>${row.desc}</b> (${row.name}):<br>类型: ${row.type}<br>默认值: ${row.default_value}`,
        "",
        {
          dangerouslyUseHTMLString: true,
          inputValue: row.value,
          closeOnClickModal: false,
        }
      ).then(({ value }) => {
        this.$axios
          .put(`${bind_url}/${row.name}`, { value: value })
          .then((res) => {
            if (res?.data?.code === 0) {
              let data = res.data.data;
              row.value = data.value;
              this.$message.success("设置成功!");
              this.refresh_all_items(true, row.category);
            } else {
              this.$message.error(
                `设置失败! (code: ${res.data?.code}; message: ${res.data?.message})`
              );
            }
          });
      });
    },
    on_toggle_button_clicked(row) {
      this.$axios
        .put(`${bind_url}/${row.name}`, {
          value: row.value ? "0" : "1",
        })
        .then((res) => {
          if (res?.data?.code === 0) {
            let data = res.data.data;
            row.value = data.value;
            this.$message.success("设置成功!");
            this.refresh_all_items(true, row.category);
          } else {
            this.$message.error(
              `设置失败! (code: ${res.data?.code}; message: ${res.data?.message})`
            );
          }
        })
        .catch((err) => {
          this.$message.error(`设置失败! (${err})`);
        });
    },
    span_method(cell) {
      let column_index = cell.columnIndex;
      if (!cell.row.editable) {
        if (column_index === 2) {
          return [1, 2];
        }
        if (column_index === 3) {
          return [0, 0];
        }
      }
    },
    format_date(timestamp, pattern = "YYYY-MM-DD HH:mm:ss") {
      return moment(Number(timestamp) * 1000).format(pattern);
    },
  },
  mounted() {
    this.refresh_all();
  },
  data() {
    return {
      items: [],
      loading: true,
    };
  },
};
</script>
