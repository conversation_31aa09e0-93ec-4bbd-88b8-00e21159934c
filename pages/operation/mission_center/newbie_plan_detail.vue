<template>
    <div class="task-push-container">
        <h1>{{ isEditMode ? '查看任务推送' : '创建任务推送' }}</h1>
        <div>
            推送状态: <span style="color: red; font-weight: bold; font-size: 20px;">{{ pushSetting.status === "DRAFT" ?
                '待提交' : pushStatus }}</span>
        </div>
        <div class="action-buttons">
            <el-button @click="goBack">返回</el-button>

            <el-button @click="saveAsDraft" v-if="!editDisabled">保存</el-button>

            <el-button type="primary" @click="updateContent" v-if="isUpdateContent">更新</el-button>

            <el-button type="primary" @click="submitForReview" v-if="!editDisabled && !copy">提交审核</el-button>

            <el-button v-if="canReview" @click="updatePlanReviewStatus('REJECTED')">审核不通过</el-button>
            <el-button type="primary" @click="updatePlanReviewStatus('PASSED')" v-if="canReview">审核通过</el-button>
        </div>

        <!-- 推送设置 -->
        <el-card shadow="never" class="section-card">
            <div slot="header" class="card-header">
                <span>推送设置</span>
                <i class="el-icon-arrow-down"></i>
            </div>
            <el-form label-width="120px" ref="pushSettingForm" :model="pushSetting" :rules="pushSettingRules"
                :disabled="editDisabled">
                <el-form-item label="推送场景：" prop="scene_type">
                    <el-select v-model="pushSetting.scene_type" placeholder="新手任务">
                        <el-option label="新手任务" value="NEWBIE" />
                        <!-- 添加其他场景类型选项 -->
                    </el-select>
                </el-form-item>

                <el-form-item label="推送名称：" prop="name" style="width: 60%;">
                    <el-input v-model="pushSetting.name" placeholder="请输入名称，不会对用户展示" maxlength="50" show-word-limit />
                </el-form-item>

                <el-form-item label="业务方：" prop="business_party">
                    <el-select v-model="pushSetting.business_party" placeholder="请选择业务方">
                        <el-option v-for="(value, key) in BusinessParties" :key="key" :label="value" :value="key">
                        </el-option>
                    </el-select>
                </el-form-item>

                <!-- 渠道选择移到 logic_params -->
                <el-form-item label="推送渠道：" required>
                    <el-radio-group v-model="channel" @change="(e) => this.pushSetting.logic_params = {}">
                        <el-radio v-for="(value, key) in Channels" :key="key" :label="key">{{ value }}</el-radio>
                    </el-radio-group>
                </el-form-item>
                <!-- 条件设置也移到 logic_params -->
                <el-form-item v-if="channel === 'NORMAL'" class="condition-container" prop="logic_params">
                    <div class="condition-row">
                        <el-input value="注册地区" disabled class="condition-label">
                        </el-input>

                        <el-select class="condition-operator" disabled :value="`in`">
                            <el-option label="in" value="in">in</el-option>
                        </el-select>

                        <el-select v-model="pushSetting.logic_params['REGISTRATION_AREA_IN']" placeholder="全部"
                            filterable clearable multiple style="width: 500px;">
                            <el-option v-for="(label, value) in Countries" :key="value" :label="label" :value="value">
                            </el-option>
                        </el-select>
                    </div>
                </el-form-item>

                <!-- 邀请注册条件 -->
                <el-form-item v-if="channel === 'REFERER'" class="condition-container" prop="logic_params">
                    <div class="condition-row">
                        <el-input value="推荐人ID或邮箱" disabled class="condition-label">
                        </el-input>

                        <el-select disabled class="condition-operator" :value="`in`">
                            <el-option label="in" value="in"></el-option>
                        </el-select>

                        <el-input v-model="pushSetting.logic_params['REFERER_ID_IN']" placeholder="空"
                            class="condition-value">
                        </el-input>
                    </div>
                </el-form-item>

                <el-form-item label="推送人数：">
                    <el-input-number v-model="pushSetting.total" placeholder="不填则无上限" :min="0" :precision="0"
                        @change="handleTotalChange">
                    </el-input-number>

                    <span v-if="pushSetting.total === 0" style="margin-left: 10px; color: #909399;">无上限</span>
                </el-form-item>

                <el-form-item label="推送时间：" prop="start_at">
                    <div class="time-picker">
                        <el-date-picker v-model="pushSetting.start_at" type="datetime" format="yyyy-MM-dd HH:mm:ss"
                            value-format="timestamp" :picker-options="pickerOptions" placeholder="开始时间 (UTC+8)"
                            @change="handleStartTimeChange" />
                        <span class="separator">-</span>
                        <el-date-picker v-model="pushSetting.end_at" type="datetime" format="yyyy-MM-dd HH:mm:ss"
                            value-format="timestamp" :picker-options="pickerOptions" placeholder="结束时间" />
                    </div>
                </el-form-item>

                <el-form-item label="推送文案：">
                    <MessageEditor ref="message-editor" class="compact-editor" :messageConfig="messageConfig"
                        :contents="deliver_content" :languages="languages"
                        :disabled="editDisabled && !isUpdateContent" />
                </el-form-item>
            </el-form>
        </el-card>

        <!-- 任务设置 -->
        <el-card shadow="never" class="section-card">
            <div slot="header" class="card-header">
                <span>任务设置</span>
                <i class="el-icon-arrow-down"></i>
            </div>
            <el-button type="primary" class="add-task-btn" @click="addTask" :disabled="editDisabled">添加任务</el-button>

            <!-- 任务列表 -->
            <draggable v-model="tasks" handle=".drag-handle" @start="dragStart" @end="dragEnd" :disabled="editDisabled">
                <transition-group>
                    <div v-for="(task, index) in tasks" :key="index" class="task-item">
                        <div class="task-header">
                            <div class="task-header-left">
                                <i class="el-icon-rank drag-handle"></i>
                                <span class="task-number">任务{{ task.sequence }}</span>
                            </div>
                            <el-button type="danger" circle icon="el-icon-close" size="mini" @click="removeTask(index)"
                                :disabled="editDisabled"></el-button>
                        </div>
                        <el-form :ref="'taskForm' + index" :model="task" :rules="taskRules" label-width="120px"
                            :disabled="editDisabled">
                            <el-form-item label="任务类型：" prop="mission_condition">
                                <el-select v-model="task.mission_condition" placeholder="请选择"
                                    @change="handleMissionConditionChange(task)">
                                    <el-option v-for="(label, value) in MissionCondition" :key="value" :label="label"
                                        :value="value">
                                    </el-option>
                                </el-select>
                            </el-form-item>

                            <el-form-item label="任务条件：" prop="logic_params">
                                <div class="condition-row">
                                    <el-input :value="getConditionLabel(task.mission_condition)" disabled
                                        class="condition-label">
                                    </el-input>

                                    <el-select class="condition-operator" :value="`>=`" disabled
                                        v-if="task.mission_condition != 'COPY_TRADING_ONCE' && task.mission_condition != 'DEMO_TRADING_ONCE'">
                                        <el-option label=">=" value=">="></el-option>
                                    </el-select>

                                    <el-input-number v-model="task.logic_params[task.mission_condition]"
                                        class="condition-value" :min="0" :precision="0"
                                        v-if="task.mission_condition != 'COPY_TRADING_ONCE' && task.mission_condition != 'DEMO_TRADING_ONCE'"></el-input-number>
                                </div>
                            </el-form-item>

                            <el-form-item label="任务周期：" prop="deadline_days">
                                <el-input-number v-model="task.deadline_days" placeholder="请输入有效天数" class="cycle-input"
                                    :min="1" :precision="0"></el-input-number>
                                <span class="unit-text">日</span>
                            </el-form-item>

                            <!-- 奖励设置 -->
                            <el-form-item label="奖励类型：" prop="reward_type">
                                <el-radio-group v-model="task.reward_type" @change="() => task.equity_id = null">
                                    <el-radio label="AIRDROP">空投奖励</el-radio>
                                    <el-radio label="CASHBACK">权益奖励</el-radio>
                                </el-radio-group>
                            </el-form-item>

                            <!-- 奖励内容 -->
                            <el-form-item prop="reward_content" v-if="task.reward_type === 'AIRDROP'">
                                <el-select v-model="task.asset" placeholder="选择币种" class="token-select">
                                    <el-option label="CET" value="CET"></el-option>
                                </el-select>

                                <el-input-number v-model="task.amount" placeholder="数量" class="token-amount"
                                    :precision="0" :min="0"></el-input-number>

                                <span class="unit-text">个</span>
                            </el-form-item>
                            <el-form-item v-else prop="reward_content">
                                <el-select v-model="selectedReward" placeholder="请选择">
                                    <el-option label="手续费返现" value="手续费返现" key="手续费返现">手续费返现</el-option>
                                </el-select>
                                <el-select v-model="task.equity_id" placeholder="选择权益类型" class="equity-select"
                                    filterable clearable>
                                    <el-option v-for="(value, key) in equityOptions" :key="key" :label="value"
                                        :value="key">
                                    </el-option>
                                </el-select>
                            </el-form-item>
                        </el-form>
                    </div>
                </transition-group>
            </draggable>
        </el-card>
    </div>
</template>

<script>
import MessageEditor from '@/components/MessageEditor';
import draggable from 'vuedraggable';
import tinymce from "tinymce/tinymce";
// 添加常量定义
const SceneType = {};

const MissionCondition = {};

export default {
    components: {
        MessageEditor,
        draggable
    },
    provide() {
        return { testSendFunc: null };
    },
    data() {
        return {
            missionPlanId: this.$route.query.id || 0,
            copy: this.$route.query.copy === 'true' || false,
            isEditMode: false,
            editDisabled: false,
            canReview: false,
            languages: {},
            pushStatus: "",
            messageConfig: {
                template_name: "newbie_plan",
                has_title: false,
                use_editor: true,
                cur_lang: 'EN_US',
                extr_params: {},
                no_translate: true,
                no_save: true,
                no_single_save: true,
                check_all_content_filled: true,
                content_label: '文案内容',
                allow_content_null: true,
            },
            deliver_content: {},
            channel: "NORMAL",
            pushSetting: {
                scene_type: "NEWBIE",
                business_party: null,
                name: '',
                logic_params: {
                    "REGISTRATION_AREA_IN": []
                },
                total: null,
                start_at: null,
                end_at: null,
            },
            tasks: [],
            Channels: {},
            Countries: {},
            BusinessParties: {},
            isDragging: false,
            isUpdateContent: false,
            equityOptions: {},
            // 添加常量到组件中使其可在模板中使用
            SceneType,
            MissionCondition,
            pickerOptions: {
                disabledDate(time) {
                    return time.getTime() < Date.now() - 24 * 60 * 60 * 1000;
                },
            },
            pushSettingRules: {
                scene_type: [
                    { required: true, message: '请选择推送场景', trigger: 'change' }
                ],
                business_party: [
                    { required: true, message: '请选择业务方', trigger: 'change' }
                ],
                name: [
                    { required: true, message: '请输入推送名称', trigger: 'blur' },
                    { max: 50, message: '长度不能超过50个字符', trigger: 'blur' }
                ],
                logic_params: [
                    {
                        validator: (rule, value, callback) => {
                            if (this.channel === 'REFERER' && !value['REFERER_ID_IN']) {
                                callback(new Error('请输入推荐人ID或邮箱'));
                            } else {
                                callback();
                            }
                        },
                        trigger: 'change'
                    }
                ],
                start_at: [
                    { required: true, message: '请选择开始日期', trigger: 'blur' }
                ],
            },
            taskRules: {
                mission_condition: [
                    { required: true, message: '请选择任务类型', trigger: 'change' }
                ],
                logic_params: [
                    {
                        validator: (rule, value, callback) => {
                            const task = this.tasks.find(t => t.logic_params === value);
                            if (!task || (task.mission_condition != 'COPY_TRADING_ONCE' && task.mission_condition != 'DEMO_TRADING_ONCE' && !value[task.mission_condition])) {
                                callback(new Error('请输入任务条件值'));
                            } else {
                                callback();
                            }
                        },
                        trigger: 'blur'
                    }
                ],
                deadline_days: [
                    { required: true, message: '请输入任务周期', trigger: 'blur' },
                    { type: 'number', message: '必须为数字值', trigger: 'blur', transform: value => Number(value) }
                ],
                reward_type: [
                    { required: true, message: '请选择奖励类型', trigger: 'change' }
                ],
                reward_content: [
                    {
                        validator: (rule, value, callback) => {
                            const task = this.tasks.find(t => t.reward_type === this.reward_type);
                            if (!task) {
                                callback();
                                return;
                            }

                            if (task.reward_type === 'AIRDROP') {
                                if (!task.asset || !task.amount) {
                                    callback(new Error('请选择代币'));
                                } else {
                                    callback();
                                }
                            } else if (task.reward_type === 'CASHBACK') {
                                if (!task.equity_id) {
                                    callback(new Error('请选择权益类型'));
                                } else {
                                    callback();
                                }
                            } else {
                                callback();
                            }
                        },
                        trigger: 'change'
                    }
                ]
            },
            selectedReward: '手续费返现',
        }
    },
    mounted() {
        this.loadMissionPlanData();
        if (this.missionPlanId !== '0') {
            this.isEditMode = true;
        } else {
            // 初始化新建模式的数据
            this.initializeNewData();
        }
    },
    methods: {
        handleTotalChange(value) {
            if (value < 1) {
                this.pushSetting.total = 0;
            }
        },
        // 初始化新建模式的数据
        initializeNewData() {
            this.pushSetting = {
                scene_type: "NEWBIE",
                name: '',
                business_party: null,
                logic_params: {
                    "REGISTRATION_AREA_IN": []
                },
                total: null,
                start_at: null,
                end_at: null,
                status: "DRAFT"
            };

            // 初始化多语言内容
            for (let lang in this.languages) {
                this.deliver_content[lang] = { content: '' };
            }
        },

        // 任务操作方法
        addTask() {
            if (this.tasks.length >= 5) {
                this.$message.error('最多只能添加5个任务');
                return;
            };
            this.tasks.push({
                mission_condition: 'DEPOSIT_AMOUNT',
                logic_params: {
                    "DEPOSIT_AMOUNT": null
                },
                sequence: this.tasks.length + 1,
                equity_id: null,
                deadline_days: null,
                reward_type: 'AIRDROP',
                asset: '',
                id: null,
                amount: null,
            });
        },
        updateTaskIndex() {
            // 更新所有任务的sequence
            this.tasks.forEach((task, index) => {
                task.sequence = index + 1;
            });
        },
        removeTask(index) {
            this.tasks.splice(index, 1);
            this.updateTaskIndex()
        },
        goBack() {
            this.$confirm('确认返回上一页？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.$router.push('/operation/mission_center/newbie_plan');
            }).catch(() => { });
        },

        // 后端接口方法
        loadMissionPlanData() {
            this.$axios.get(`/api/operation/mission/${this.missionPlanId}`)
                .then(res => {
                    if (res.data.code === 0) {
                        const data = res.data.data;
                        this.pushStatus = data.status_value;
                        this.SceneType = data.scene_types;
                        this.languages = data.languages;
                        this.MissionCondition = data.conditions;
                        this.Channels = data.channels;
                        this.Countries = data.countries;
                        this.equityOptions = data.rewards;
                        this.BusinessParties = data.business_parties;
                        if (this.missionPlanId == 0) {
                            for (let lang in this.languages) {
                                this.deliver_content[lang] = { content: '' };
                            }
                            return
                        }

                        // 填充推送设置
                        this.channel = data.channel;
                        let logic_params = data.logic_params;
                        if (this.channel == 'REFERER') {
                            logic_params['REFERER_ID_IN'] = logic_params['REFERER_ID_IN'].join(',')
                        }
                        this.pushSetting = {
                            scene_type: data.scene_type || 'NEWBIE',
                            name: data.name || '',
                            logic_params: data.logic_params,
                            business_party: data.business_party,
                            total: data.total,
                            start_at: data.start_at * 1000,
                            end_at: data.end_at ? data.end_at * 1000 : data.end_at,
                            status: data.status
                        };

                        // 填充推送文案
                        for (let lang in this.languages) {
                            this.deliver_content[lang] = { content: data.deliver_content ? data.deliver_content[lang] : '' };
                        }

                        // 填充任务设置
                        if (data.missions && data.missions.length > 0) {
                            this.tasks = data.missions.map(task => ({
                                mission_condition: task.mission_condition,
                                logic_params: task.logic_params,
                                deadline_days: task.deadline_days,
                                reward_type: task.reward_type,
                                asset: task.reward_type == 'AIRDROP' ? task.asset : null,
                                amount: task.reward_type == 'AIRDROP' ? task.amount : null,
                                sequence: task.sequence,
                                equity_id: task.equity_id ? String(task.equity_id) : null,
                                id: this.copy ? null : task.id
                            }));
                        }

                        this.isEditMode = true;
                        let is_self_record = data.is_self_record
                        if (this.copy) {
                            this.pushSetting.status = "DRAFT"
                            is_self_record = true
                        }
                        this.editDisabled = !["DRAFT", "REJECTED"].includes(this.pushSetting.status) || !is_self_record
                        this.canReview = this.pushSetting.status == "PENDING"
                        this.isUpdateContent = this.pushSetting.status == "EFFECTIVE"
                    } else {
                        this.$message.error(res.data.message || '加载数据失败');
                    }
                })
                .catch(err => {
                    this.$message.error(`加载数据失败: ${err.message || '未知错误'}`);
                });
        },

        saveAsDraft() {
            this.saveMissionPush();
        },

        submitForReview() {
            this.handleSubmit('review');
        },
        updatePlanReviewStatus(status) {
            this.handleSubmit('review_status', {
                status: status,
                successMessage: status === 'PASSED' ? '已审核成功' : '已拒绝审核'
            });
        },
        saveMissionPush() {
            this.handleSubmit('save');
        },

        // Drag and drop methods
        dragStart() {
            this.isDragging = true;
            // Add any additional logic when drag starts
        },

        dragEnd() {
            this.isDragging = false;
            this.updateTaskIndex()
        },

        // Helper methods for task rewards
        getConditionLabel(missionCondition) {
            switch (missionCondition) {
                case 'DEPOSIT_AMOUNT':
                    return '累计入金额 (USDT)';
                case 'SPOT_AMOUNT':
                    return '累计交易额 (USDT)';
                case 'PERPETUAL_AMOUNT':
                    return '累计交易额 (USDT)';
                case 'COPY_TRADING_ONCE':
                    return '完成一次跟单';
                case 'DEMO_TRADING_ONCE':
                    return '完成一次模拟交易';
                default:
                    return '';
            }
        },

        handleMissionConditionChange(task) {
            let key = task.mission_condition
            if (key == 'COPY_TRADING_ONCE' || key == 'DEMO_TRADING_ONCE') {
                task.logic_params = {
                    ASSET: '',
                    [key]: 1
                };
            } else {
                task.logic_params = {};
            }
        },

        getConditionUnit(missionCondition) {
            switch (missionCondition) {
                case 'DEPOSIT_AMOUNT':
                    return 'USDT';
                case 'SPOT_AMOUNT':
                    return 'USDT';
                case 'PERPETUAL_AMOUNT':
                    return 'USDT';
                default:
                    return '';
            }
        },
        // 调整时间为最近的分钟整点
        adjustToNearestMinute(timestamp) {
            if (!timestamp) return null;
            const date = new Date(timestamp);

            // 如果当前已经是分钟整点，直接返回
            if (date.getSeconds() === 0 && date.getMilliseconds() === 0) {
                return date.getTime();
            }

            // 否则调整到下一个分钟整点
            date.setSeconds(0);
            date.setMilliseconds(0);
            date.setMinutes(date.getMinutes() + 1);
            return date.getTime();
        },

        // 处理开始时间变化
        handleStartTimeChange(value) {
            if (!value) return;

            const selectedDate = new Date(value);
            const now = new Date();
            const nextTime = this.adjustToNearestMinute(now);
            // 判断是否是当天
            const isChange = selectedDate.getDate() === now.getDate() &&
                selectedDate.getMonth() === now.getMonth() &&
                selectedDate.getFullYear() === now.getFullYear() && selectedDate < nextTime;

            if (isChange) {
                // 如果是当天，调整为下一个整点
                this.pushSetting.start_at = nextTime;
            } else {
                // 如果不是当天，直接使用选择的时间
                this.pushSetting.start_at = this.adjustToNearestMinute(value);
            }
        },

        // 新增：统一的提交处理方法
        async handleSubmit(submitType, options = {}) {
            const {
                status = null,  // 状态检查
                successMessage = '',  // 成功提示
                errorMessage = '',  // 错误提示
                apiEndpoint = '',  // API端点
                method = 'post',  // 请求方法
                extraData = {}  // 额外数据
            } = options;

            // 状态检查
            if (status && !status.includes(this.pushSetting.status)) {
                this.$message.error('当前状态不允许此操作');
                return;
            }

            // 表单验证
            const isValid = await this.validateForms();
            if (!isValid) return;

            // 构建基础数据
            const submitData = {
                ...this.buildSubmitData(),
                ...extraData
            };

            try {
                const res = await this.$axios[method](apiEndpoint, submitData);
                if (res.data.code === 0) {
                    this.$message.success(successMessage);

                    // 处理保存后的逻辑
                    if (['save', 'review'].includes(submitType)) {
                        this.missionPlanId = res.data.data.id;
                        this.copy = this.copy ? false : this.copy;
                        const targetQuery = { id: this.missionPlanId, copy: this.copy };
                        const targetRoute = this.$router.resolve({ query: targetQuery }).route;
                        if (targetRoute.fullPath !== this.$route.fullPath) {
                            this.$router.replace({ query: targetQuery });
                        }
                    }

                    // 重新加载数据
                    this.loadMissionPlanData();
                } else {
                    this.$message.error(res.data.message || errorMessage);
                    if (submitType == 'review_status') {
                        this.loadMissionPlanData();
                    }
                }
            } catch (err) {
                this.$message.error(`${errorMessage}: ${err.message || '未知错误'}`);
            }
        },

        // 重构后的保存方法
        async saveMissionPush() {
            const apiEndpoint = this.isEditMode && !this.copy ?
                `/api/operation/mission/${this.missionPlanId}` :
                '/api/operation/mission';
            const method = this.isEditMode && !this.copy ? 'put' : 'post';

            await this.handleSubmit('save', {
                successMessage: this.isEditMode ? '已更新草稿' : '已保存为草稿',
                errorMessage: '保存失败',
                apiEndpoint,
                method
            });
        },

        // 重构后的提交审核方法
        async submitForReview() {
            await this.handleSubmit('review', {
                status: ["DRAFT", "REJECTED"],
                successMessage: '提交审核成功',
                errorMessage: '提交审核失败',
                apiEndpoint: `/api/operation/mission/${this.missionPlanId}`,
                method: 'patch',
                extraData: {
                    status: 'PENDING'  // 可以添加额外的状态信息
                }
            });
        },

        // 重构后的审核通过方法
        async updatePlanReviewStatus(status) {
            const statusConfig = {
                'PASSED': {
                    confirmText: '确认审核通过此任务推送?<br><span style="color: #999; font-size: 14px;">(通过后任务推送立即生效)</span>',
                    successMessage: '已审核成功'
                },
                'REJECTED': {
                    confirmText: '确认审核不通过',
                    successMessage: '已拒绝审核'
                }
            };

            const config = statusConfig[status];

            try {
                await this.$confirm(config.confirmText, '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning',
                    dangerouslyUseHTMLString: true,
                    customClass: 'custom-confirm-dialog',
                    center: true
                });

                await this.handleSubmit('review_status', {
                    successMessage: config.successMessage,
                    errorMessage: '审核操作失败',
                    apiEndpoint: `/api/operation/mission/${this.missionPlanId}/review`,
                    method: 'post',
                    extraData: { status }
                });
            } catch (err) {
                if (err !== 'cancel') {
                    this.$message.error('审核操作失败');
                } else {
                    this.$message({
                        type: 'info',
                        message: '已取消审核'
                    });
                }
            }
        },

        validateLanguageContent() {
            for (let lang in this.deliver_content) {
                let content = tinymce.get(lang).getContent({ format: "text" })
                if (content.length > 100) {
                    this.$message.error(`${this.languages[lang]} 文案内容不能超过100个字符`);
                    return false
                }
            }
            return true
        },

        // 验证表单
        validateForms() {
            return new Promise((resolve, reject) => {
                this.$refs.pushSettingForm.validate(valid => {
                    if (!valid) {
                        this.$message.error('请完善推送设置信息');
                        reject(false);
                    }

                    // 检查任务是否有重复的任务类型和条件值
                    const taskMap = new Map();
                    let hasDuplicate = false;
                    this.tasks.forEach(task => {
                        const key = `${task.mission_condition}-${task.logic_params[task.mission_condition]}`;
                        if (taskMap.has(key)) {
                            hasDuplicate = true;
                            const existingTask = this.tasks.find(t => t.sequence === taskMap.get(key));
                            this.$message.error(`任务${task.sequence}和任务${existingTask.sequence}的任务类型和条件值重复`);
                        } else {
                            taskMap.set(key, task.sequence);
                        }
                    });

                    if (hasDuplicate) {
                        reject(false);
                    }

                    if (this.channel === 'REFERER') {
                        const paramsValues = Object.values(this.pushSetting.logic_params)
                        const hasEmpty = paramsValues.some(value => {
                            let fmtValue = value.trim();
                            return value === null || value === undefined || fmtValue === '';
                        });
                        if (hasEmpty || paramsValues.length == 0) {
                            this.$message.error('请完善推送渠道配置。')
                            reject(false)
                        }
                    }

                    if (this.pushSetting.end_at && this.pushSetting.end_at < this.pushSetting.start_at) {
                        this.$message.error('结束时间不能小于开始时间');
                        reject(false);
                    }
                    if (!this.validateLanguageContent()) {
                        reject(false);
                    }
                    // 验证任务列表是否为空
                    if (this.tasks.length === 0) {
                        this.$message.error('请至少添加一个任务');
                        reject(false);
                    }

                    // 验证每个任务的表单
                    const taskValidations = this.tasks.map((task, index) => {
                        return new Promise((resolve) => {
                            const formRef = this.$refs[`taskForm${index}`];
                            if (formRef && formRef.length > 0) {
                                formRef[0].validate(valid => {
                                    if (!valid) {
                                        this.$message.error(`第${index + 1}个任务信息不完整`);
                                        reject(false);
                                    }
                                    if (task.reward_type === 'AIRDROP') {
                                        if (!task.asset || !task.amount) {
                                            this.$message.error(`第${index + 1}个任务, 请填写空投奖励的币种和数量`);
                                            reject(false);
                                        }
                                    } else if (task.reward_type === 'CASHBACK') {
                                        if (!task.equity_id) {
                                            this.$message.error(`第${index + 1}个任务, 请选择权益.`)
                                            reject(false);
                                        }
                                    }
                                    resolve(true);
                                });
                            } else {
                                resolve(false);
                            }
                        });
                    });

                    Promise.all(taskValidations).then(results => {
                        if (results.every(valid => valid)) {
                            resolve(true);
                        } else {
                            reject(false);
                        }
                    });
                });
            });
        },

        // 构建提交数据
        // 新增：构建提交数据方法
        buildSubmitData() {
            const submitData = {
                id: this.copy ? null : this.missionPlanId,
                scene_type: this.pushSetting.scene_type,
                business_party: this.pushSetting.business_party,
                name: this.pushSetting.name,
                logic_params: this.pushSetting.logic_params,
                total: this.pushSetting.total,
                start_at: this.pushSetting.start_at,
                end_at: this.pushSetting.end_at,
                deliver_content: {},
                channel: this.channel,
                missions: this.tasks.map(task => ({
                    mission_condition: task.mission_condition,
                    logic_params: { ...task.logic_params, ...{ ASSET: this.getConditionUnit(task.mission_condition) } },
                    deadline_days: task.deadline_days,
                    sequence: task.sequence,
                    asset: task.asset,
                    amount: task.amount,
                    id: task.id,
                    equity_id: task.equity_id ? Number(task.equity_id) : null,
                    reward_type: task.reward_type
                }))
            };

            // 处理多语言内容
            for (let lang in this.deliver_content) {
                if (this.deliver_content[lang].content) {
                    submitData.deliver_content[lang] = this.deliver_content[lang].content;
                }
            }

            return submitData;
        },
        updateContent() {
            this.$confirm('确认更新任务推送文案?<br><span style="color: #999; font-size: 14px;">(文案更新无需审核会立即生效)</span>', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
                dangerouslyUseHTMLString: true,
                customClass: 'custom-confirm-dialog',
                center: true
            }).then(() => {
                const submitData = this.buildSubmitData();
                if (!this.validateLanguageContent()) {
                    return
                }
                this.$axios.put(`/api/operation/mission/${this.missionPlanId}/update-content`, {
                    deliver_content: submitData.deliver_content
                })
                    .then(res => {
                        if (res.data.code == 0) {
                            this.$message.success('更新成功');
                        } else {
                            this.$message.error(res.data.message);
                        }
                    })
                    .catch(err => {
                        this.$message.error(err.response.data.message);
                    });
            }).catch(() => {
                this.$message.info('已取消更新');
                // 重新加载数据并刷新富文本编辑器内容
                this.reloadDataAndRefreshEditor();
            });
        },

        // 重新加载数据并刷新富文本编辑器
        reloadDataAndRefreshEditor() {
            this.$axios.get(`/api/operation/mission/${this.missionPlanId}`)
                .then(res => {
                    if (res.data.code === 0) {
                        const data = res.data.data;

                        // 填充推送文案并刷新富文本编辑器
                        this.$nextTick(() => {
                            for (let lang in this.languages) {
                                const content = data.deliver_content ? data.deliver_content[lang] : '';
                                this.deliver_content[lang] = { content: content };

                                // 刷新TinyMCE编辑器内容
                                const editor = tinymce.get(lang);
                                if (editor) {
                                    editor.setContent(content || '');
                                }
                            }
                        });
                    } else {
                        this.$message.error(res.data.message || '重新加载数据失败');
                    }
                })
                .catch(err => {
                    this.$message.error(`重新加载数据失败: ${err.message || '未知错误'}`);
                });
        }
    }
}
</script>

<style scoped>
.task-push-container {
    padding: 20px;
}

h1 {
    font-size: 20px;
    margin-bottom: 20px;
}

.action-buttons {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 20px;
}

.section-card {
    margin-bottom: 20px;
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.group-input {
    width: 100%;
}

.time-picker {
    display: flex;
    align-items: center;
}

.separator {
    margin: 0 10px;
}

.add-task-btn {
    margin-bottom: 20px;
}

.task-item {
    margin: 20px 0;
    padding: 15px;
    border: 1px solid #ebeef5;
    border-radius: 4px;
    position: relative;
}

.task-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.task-header-left {
    display: flex;
    align-items: center;
}

.drag-handle {
    cursor: move;
    font-size: 16px;
    color: #909399;
    margin-right: 10px;
    padding: 2px;
}

.drag-handle:hover {
    color: #409EFF;
}

.task-number {
    font-weight: bold;
    color: #f56c6c;
}

.condition-input {
    display: flex;
    align-items: center;
    gap: 10px;
}

.condition-input .el-input {
    width: 120px;
}

.date-unit {
    margin-left: 5px;
}

.token-input {
    width: 120px;
}

.token-unit {
    margin-left: 5px;
}

.fee-discount {
    display: flex;
    align-items: center;
    gap: 10px;
}

.fee-discount .el-input {
    width: 120px;
}

/* 推送圈群样式 */
.push-group-container {
    width: 100%;
}

.condition-card {
    border: 1px solid #ebeef5;
    border-radius: 4px;
    background-color: #fff;
    overflow: hidden;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.02);
    margin-bottom: 15px;
}

.condition-header {
    padding: 12px 15px;
    background-color: #f5f7fa;
    display: flex;
    align-items: center;
    border-bottom: 1px solid #ebeef5;
}

.condition-header i {
    margin-right: 8px;
    color: #409EFF;
}

.condition-content {
    padding: 15px;
}

.condition-row {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

.condition-operator {
    width: 80px;
    flex-shrink: 0;
}

.condition-hint {
    font-size: 12px;
    color: #909399;
    margin-top: 5px;
}

.condition-hint i {
    margin-right: 5px;
}

.mining-pool-notice {
    display: flex;
    align-items: center;
    background-color: #f0f9eb;
    padding: 10px 15px;
    border-radius: 4px;
    color: #67c23a;
}

.mining-pool-notice i {
    margin-right: 8px;
    font-size: 16px;
}

.condition-container {
    padding: 0;
}

.condition-row {
    display: flex;
    align-items: center;
    background-color: #fff;
    padding: 10px;
    border-radius: 4px;
    border: 1px solid #dcdfe6;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.03);
}

.condition-label {
    width: 180px;
    margin-right: 10px;
    background-color: #f5f7fa;
}

.condition-operator {
    width: 80px;
    margin: 0 10px;
}

.condition-value {
    width: 200px;
    margin-left: 10px;
}

.no-condition {
    color: #909399;
    font-style: italic;
    padding: 10px;
    background-color: #f5f7fa;
    border-radius: 4px;
}

/* Transition styles for drag and drop */
.task-item {
    transition: all 0.3s;
}

.sortable-ghost {
    opacity: 0.5;
    background: #c8ebfb;
}

.sortable-drag {
    opacity: 0.8;
    background: #f9f9f9;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

/* Reward styles */
.reward-container {
    display: flex;
    align-items: center;
    background-color: #f9f9f9;
    padding: 15px;
    border-radius: 4px;
    margin-left: 120px;
    /* Align with form items that have labels */
}

.token-select,
.fee-discount-select {
    width: 180px;
    margin-right: 10px;
}

.token-amount,
.fee-discount-value {
    width: 180px;
    margin-right: 10px;
}

.unit-text {
    margin-left: 5px;
    color: #606266;
}

.cycle-input {
    width: 180px;
}

/* 添加到现有的 <style> 部分 */
:deep(.custom-confirm-dialog) {
    border-radius: 8px;
}

:deep(.custom-confirm-dialog .el-message-box__header) {
    padding-right: 20px;
}

:deep(.custom-confirm-dialog .el-message-box__content) {
    padding: 30px 20px;
    text-align: center;
}

:deep(.custom-confirm-dialog .el-message-box__btns) {
    padding: 10px 20px 20px;
}

:deep(.custom-confirm-dialog .el-button) {
    border-radius: 4px;
    padding: 10px 20px;
    min-width: 100px;
}

:deep(.custom-confirm-dialog .el-button--default) {
    background-color: #f5f5f5;
    border-color: #f5f5f5;
    color: #333;
}

:deep(.custom-confirm-dialog .el-button--primary) {
    background-color: #409EFF;
}

:deep(.custom-confirm-dialog .el-message-box__headerbtn) {
    font-size: 18px;
    top: 15px;
    right: 15px;
}


/* 调整 MessageEditor 的宽度和位置 */
.compact-editor {
    width: 80%;
    margin: 0;
}

.compact-editor :deep(.tox-tinymce) {
    width: 100% !important;
}

.compact-editor :deep(.tox .tox-edit-area__iframe) {
    width: 100% !important;
}

.compact-editor :deep(.tox .tox-edit-area) {
    width: 100% !important;
}

/* 只增加特定 equity_id select 的宽度 */
:deep(.equity-select) {
    width: 260px !important;
}
</style>
