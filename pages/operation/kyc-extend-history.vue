<template>
  <div class="table-data" id="table-frame">
    <h2 style="font-family: 'Microsoft YaHei',Arial,sans-serif;">
      补充认证第三方审核记录
    </h2>

    <el-form :inline="true">
      <el-form-item label="流水号">
        <el-input v-model="filters.transaction_id"
                  clearable
                  placeholder="请输入"
                  style="width: 160px;">
        </el-input>
      </el-form-item>
      <el-form-item label="用户">
        <el-input v-model="filters.user"
                  clearable
                  placeholder="ID/邮箱/用户名/手机号"
                  @change="handle_user_keyword_input"
                  style="width: 160px;"></el-input>
      </el-form-item>
      <el-form-item label="国家">
        <el-select v-model="filters.nationality"
                   clearable
                   filterable
                   placeholder="<ALL>"
                   style="width: 120px;">
          <el-option v-for="(name, code) in countries"
                     :key="code"
                     :label="name"
                     :value="code">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="审核类型">
        <el-select v-model="filters.doc_type"
                   clearable
                   placeholder="<ALL>"
                   style="width: 120px;">
          <el-option v-for="(k, v) in doc_types"
                     :key="v"
                     :label="k"
                     :value="v">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="证件类型">
        <el-select v-model="filters.id_type"
                   clearable
                   placeholder="<ALL>"
                   style="width: 120px;">
          <el-option v-for="(k, v) in id_types"
                     :key="v"
                     :label="k"
                     :value="v">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item prop="start" label="开始时间">
        <el-date-picker
          @change="handle_date_range_selection"
          v-model="filters_mid.date_range[0]"
          type="datetime"
          value-format="timestamp"
          placeholder="开始时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item prop="end" label="结束时间">
        <el-date-picker
          @change="handle_date_range_selection"
          v-model="filters_mid.date_range[1]"
          type="datetime"
          value-format="timestamp"
          placeholder="结束时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="状态">
        <el-select v-model="filters.status"
                   clearable
                   placeholder="<ALL>"
                   style="width: 120px;">
          <el-option v-for="(name, status) in statuses"
                     :key="status"
                     :label="name"
                     :value="status">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="证件号码">
        <el-input v-model="filters.id_number"
                  clearable
                  placeholder="请输入"
                  style="width: 160px;">
        </el-input>
      </el-form-item>
      <el-form-item>
        <el-tooltip content="刷新" placement="right" :open-delay="500" :hide-after="2000">
          <el-button icon="el-icon-refresh-left"
                     circle
                     @click="handle_page_refresh"></el-button>
        </el-tooltip>
      </el-form-item>
      <el-form-item>
        <el-tooltip content="导出" placement="right" :open-delay="500" :hide-after="2000">
          <el-button icon="el-icon-download"
                     circle
                     @click="handle_export"></el-button>
        </el-tooltip>
      </el-form-item>
    </el-form>

    <el-table :data="items"
              v-loading="loading"
              stripe>
      <el-table-column label="流水号">
          <template slot-scope="scope">
            <el-popover placement="left"
                        trigger="hover">
              <el-row type="flex" justify="space-around" align="middle">
                <el-col :span="21">
                  {{ scope.row.transaction_id}}
                </el-col>
                <el-col :span="2">
                  <el-tooltip content="复制" placement="top" :open-delay="500" :hide-after="2000">
                    <el-button icon="el-icon-document-copy"
                              size="mini"
                              circle
                              v-clipboard:copy="scope.row.transaction_id"
                              v-clipboard:success="handle_content_copy"/>
                  </el-tooltip>
                </el-col>
              </el-row>
            <span slot="reference">
              {{ scope.row.transaction_id }}
            </span>
            </el-popover>
          </template>
      </el-table-column>
      <el-table-column label="创建时间" prop="created_at" :formatter="row => format_date(row.created_at)" show-overflow-tooltip />
      <el-table-column label="用户邮箱"
                       show-overflow-tooltip>
        <template slot-scope="scope">
          <el-link :href="'/users/user-details?id=' + scope.row.user_id"
                   type="primary"
                   target="_blank"
                   :underline="false"
                   style="width: 100%; font-weight: normal; display: inline-block; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
            {{ scope.row.user_email || scope.row.user_id }}
          </el-link>
        </template>
      </el-table-column>
      <el-table-column label="姓名" prop="full_name" show-overflow-tooltip />
      <el-table-column label="国家" prop="nationality" :formatter="row => countries[row.nationality] || row.nationality" show-overflow-tooltip />
      <el-table-column label="审核类型" prop="doc_type" :formatter="row => doc_types[row.doc_type]" show-overflow-tooltip />
      <el-table-column label="证件类型" prop="id_type" :formatter="row => id_types[row.id_type]" show-overflow-tooltip />
      <el-table-column label="证件号码" prop="id_number" show-overflow-tooltip />
      <el-table-column label="第三方拒绝原因" prop="rejection_reason" :formatter="row => row.rejection_reason || '-'" show-overflow-tooltip />
      <el-table-column label="状态" prop="status" :formatter="row => statuses[row.status]" show-overflow-tooltip />
      <el-table-column label="操作">
        <template slot-scope="scope">
          <el-tooltip content="查看用户初级KYC认证详情" placement="top" :open-delay="500">
            <el-button type="primary" icon="el-icon-info" circle size="mini" @click="handleDetail(scope.row)" />
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>

    <el-pagination :current-page.sync="filters.page"
                   :page-size.sync="filters.limit"
                   :page-sizes="[50, 100, 200, 500]"
                   :total="total"
                   @size-change="handle_limit_change"
                   @current-change="handle_page_change"
                   :hide-on-single-page="true"
                   layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>

    <el-backtop></el-backtop>
  </div>
</template>

<style scoped>
</style>

<script>
  import moment from "moment";
  import Vue from 'vue';
  import VueClipboard from 'vue-clipboard2';

  Vue.use(VueClipboard);

  export default {
    methods: {
      get_data() {
        this.loading = true;
        this.$axios.get('/api/operation/kyc/extend/history', {params: this.filters}).then(
          res => {
            this.loading = false;
            if (res && res.data.code === 0) {
              let data = res.data.data;
              this.items = data.items;
              this.total = data.total;
              let extra = data.extra;
              this.statuses = extra.statuses;
              this.id_types = extra.id_types;
              this.doc_types = extra.doc_types;
              this.countries = extra.countries;
            } else {
              this.items = [];
              this.total = 0;
              this.$message.error(`code: ${res.data?.code}; message: ${res.data?.message}`);
            }
          }
        );
      },
      handle_status_selection() {
        this.reset_page();
        this.get_data();
      },
      handle_reason_selection() {
        this.reset_page();
        this.get_data();
      },
      handle_user_keyword_input() {
        this.reset_page();
        this.get_data();
      },
      handle_date_range_selection() {
        this.reset_page();
        this.get_data();
      },
      handle_limit_change() {
        this.reset_page();
        this.get_data();
      },
      handle_page_change() {
        this.get_data();
      },
      handle_page_refresh() {
        this.reset_page();
        this.get_data();
      },
      reset_page() {
        this.filters.page = 1;
      },
      handle_content_copy() {
        this.$message.success('内容已复制到剪贴板');
      },
      format_date(timestamp, pattern = 'YYYY-MM-DD HH:mm:ss') {
        return moment(Number(timestamp) * 1000).format(pattern);
      },
      update_router_query() {
        let query = {};
        Object.assign(query, this.filters);
        Object.keys(query).forEach((key) => !query[key] && delete query[key]);
        this.$router.replace({query: query});
      },
      handleDetail(row) {
        const routeData = this.$router.resolve({ path: '/operation/kyc-details', query: { id: row.kyc_id } });
        window.open(routeData.href, '_blank');
      },
      handle_export() {
        let filename = 'kyc_varification_extend_history.xlsx';
        let params =  {...this.filters, export: 1 }
        this.$download_from_url("/api/operation/kyc/extend/history", filename, params)
      }
    },
    created() {
      this.$sync_router_query(this, 'filters', {nationality: String, status: String, user: String, id_number: String});
      let filters = this.filters;
      if (filters.start && filters.end) {
        this.filters_mid.date_range = [new Date(filters.start * 1000), new Date(filters.end * 1000)];
      }
    },
    mounted() {
      window.onload = () => {
        return (() => {
          var tableData = document.getElementById("table-frame");
          var tableObj = document.getElementsByTagName("table")[1];
          tableData.style.width = tableObj.offsetWidth + 'px';
        })()
      }
      window.onresize = () => {
        return (() => {
          var tableData = document.getElementById("table-frame");
          var tableObj = document.getElementsByTagName("table")[1];
          tableData.style.width = tableObj.offsetWidth + 'px';
        })()
      }
      this.get_data();
    },
    data() {
      return {
        filters: {
          status: null,
          user: null,
          id_number: null,
          transaction_id: null,
          start: null,
          end: null,
          service_type: null,
          doc_type: null,
          page: null,
          limit: 50
        },
        filters_mid: {
          date_range: [null, null],
        },
        items: [],
        total: 0,
        statuses: {},
        id_types: {},
        doc_types: {},
        loading: true,
        countries: {}
      }
    },
    watch: {
      'filters_mid.date_range': function (date_range) {
        Object.assign(this.filters, {
          start: date_range && date_range[0] ? date_range[0] / 1000 : null,
          end: date_range && date_range[1] ? date_range[1] / 1000 : null
        });
      }
    }
  }
</script>
