<template>
  <div class="table-data">
    <el-row type="flex" justify="space-between" align="middle">
      <el-col :span="22">
        <h2 style="font-family: 'Microsoft YaHei',Arial,sans-serif;">
          欢迎语配置
        </h2>
      </el-col>

      <el-col :span="1">
        <el-tooltip content="新建" placement="left" :open-delay="500" :hide-after="2000">
          <el-button
            type="primary"
            icon="el-icon-plus"
            circle
            @click="handleCreate"
          ></el-button>
        </el-tooltip>
      </el-col>

      <el-col :span="1">
        <el-tooltip content="刷新" placement="right" :open-delay="500" :hide-after="2000">
          <el-button icon="el-icon-refresh-left"
                     circle
                     @click="get_data"></el-button>
        </el-tooltip>
      </el-col>
    </el-row>

    <el-table :data="items"
              v-loading="loading"
              stripe>
      <el-table-column label="ID"
                       prop="id"
                       show-overflow-tooltip
                       width="50">
      </el-table-column>

      <el-table-column label="欢迎语"
                       prop="message"
                       show-overflow-tooltip>
      </el-table-column>

      <el-table-column label="是否展示" show-overflow-tooltip>
        <template v-slot="scope">
          <el-switch :value="scope.row.status === 'SHOW'"
                     active-color="#13ce66"
                     inactive-color="#ff4949"
                     @change="value => changeStatus(scope.row, value)"></el-switch>
        </template>
      </el-table-column>

      <el-table-column label="操作">
        <template slot-scope="scope">
          <el-tooltip content='编辑'
                      placement="left" :open-delay="500" :hide-after="2000">
            <el-button icon='el-icon-edit'
                       size="mini" type="primary" circle @click="handleEdit(scope.row)"></el-button>
          </el-tooltip>
          <el-tooltip content="删除" placement="right" :open-delay="500" :hide-after="2000">
            <el-button size="mini" type="danger" icon="el-icon-delete" circle
                       @click="delete_row(scope.row)"></el-button>
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>

    <el-dialog
      :title="action === DIALOG_CREATION ? '新建欢迎语' : '编辑欢迎语'"
      :visible.sync="dialog_show"
      :before-close="handleClose"
      width="95%"
    >
      <el-form :model="dialogData" ref="dialogData" label-width="100px">
        <AITranslateTab
          ref="content-translate"
          :langs="langs"
          :contents="dialogData.contents"
          :translateAttrNames="['message']"
          business="CUSTOMER_CHATBOT"
          :businessId="dialogRowId ? dialogRowId.toString() : null"
          :showAsync="false"
          style="margin-left: 100px;"
        >
        <template v-slot="{ lang }">
            <el-form-item label="欢迎语" required>
              <span>
                <el-input v-model="dialogData.contents[lang].message" type="textarea" :rows="3"></el-input>
              </span>
            </el-form-item>
            <br/>
          </template>
        </AITranslateTab>
        <el-button type="primary" style="position: absolute; bottom: 20px; right: 20px;" :disabled="!canSubmit" @click="submit">提交</el-button>
      </el-form>
    </el-dialog>

    <el-backtop></el-backtop>
  </div>
</template>

<script>
import AITranslateTab from '@/components/AITranslateTab.vue';

export default {
  components: {
    AITranslateTab
  },
  methods: {
    get_data() {
      this.loading = true;
      this.$axios.get('/api/operation/welcome-message', {params: this.filters}).then(res => {
        this.loading = false;
        if (res && res.data.code === 0) {
          const data = res.data.data;
          this.items = data.items;
          this.total = data.total;
          this.langs = data.languages;
          this.initDialogData();
        } else {
          this.items = [];
          this.total = 0;
          this.langs = {};
          this.$message.error(`获取欢迎语列表失败: code: ${res.data?.code}; message: ${res.data?.message}`);
        }
      }).catch(err => {
        this.loading = false;
        this.$message.error(`加载失败! (${err})`);
      });
    },
    initDialogData(existContents = null) {
      let lang_list = Object.keys(this.langs);
      if (existContents) {
        const keys = new Set(Object.keys(existContents));
        lang_list = lang_list.filter(key => !keys.has(key));
      }
      let contents = Object.fromEntries(
        lang_list.map((lang) => [
          lang,
          { message: '' },
        ])
      );
      this.dialogData = {
        contents: existContents ? {...contents, ...existContents} : contents
      }
    },

    changeStatus(row, value) {
      const newStatus = value ? 'SHOW' : 'HIDE';
      const newStatusText = value ? '展示' : '隐藏';
      this.$confirm(`是否确定${newStatusText}该欢迎语？`, '提示', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$axios.patch(
          '/api/operation/welcome-message/' + row.id,
          { status: newStatus }
        ).then(
          res => {
            if (res.data.code === 0) {
              this.$message.success('操作成功!');
              this.get_data();
            } else {
              this.$message.error(`code: ${res.data?.code}; message: ${res.data?.message}`);
            }
          }
        ).catch(err => {
          this.$message.error(`修改失败! (${err})`);
        });
      }).catch(() => {

      });
    },

    handleCreate() {
      this.action = this.DIALOG_CREATION;
      this.initDialogData();
      this.dialog_show = true;
      this.dialogRowId = null;
      this.$nextTick(() => {
        this.$refs['content-translate']?.clearTranslationStatus();
      });
    },
    handleEdit(row) {
      this.action = this.DIALOG_EDIT;
      this.dialogRowId = row.id;
      this.$axios.get(
        '/api/operation/welcome-message/' + this.dialogRowId
      ).then(res => {
          if (res.data.code === 0) {
            let data = res.data.data;
            let transformedContents = {};
            Object.keys(data.contents).forEach(lang => {
              transformedContents[lang] = { message: data.contents[lang] };
            });
            this.initDialogData(transformedContents);
            this.dialog_show = true;
          } else {
            this.$message.error(`code: ${res.data?.code}; message: ${res.data?.message}`);
          }
        }
      ).catch(err => {
        this.$message.error(`加载失败! (${err})`);
      });
    },

    async submit() {
      this.canSubmit = false;
      if (!this.beforeSubmit()) {
        this.canSubmit = true;
        return;
      }
      
      let transformedContents = {};
      Object.keys(this.dialogData.contents).forEach(lang => {
        transformedContents[lang] = this.dialogData.contents[lang].message;
      });
      
      const postBody = { contents: transformedContents };

      let request;
      if (this.action === this.DIALOG_CREATION) {
        request = this.$axios.post('/api/operation/welcome-message', postBody);
      } else {
        request = this.$axios.patch('/api/operation/welcome-message/' + this.dialogRowId, postBody);
      }
      
      request.then(res => {
          if (res.data.code === 0) {
            this.$message.success('操作成功');
            this.get_data();
            this.dialog_show = false;
          } else {
            this.$message.error(`code: ${res.data?.code}; message: ${res.data?.message}`);
          }
        }
      ).catch(err => {
        this.$message.error(`操作失败! (${err})`);
      }).finally(() => {
        this.canSubmit = true;
      });
    },

    beforeSubmit() {
      let form = this.dialogData;
      let hasEnglish = form.contents['EN_US'] && form.contents['EN_US'].message.trim();
      let hasChinese = form.contents['ZH_HANS_CN'] && form.contents['ZH_HANS_CN'].message.trim();
      
      if (!hasEnglish && !hasChinese) {
        this.$message.error("请至少填写英文或中文欢迎语其中一种!");
        return false;
      }
      
      return true;
    },
    delete_row(row) {
      this.$confirm(`确认删除?`).then(() => {
        this.$axios.delete(`/api/operation/welcome-message/${row.id}`).then(res => {
          if (res?.data?.code === 0 || res?.status === 204) {
            this.get_data();
            this.$message.success("删除成功!");
          } else {
            this.$message.error(`删除失败! (code: ${res.data?.code}; message: ${res.data?.message})`);
          }
        }).catch(err => {
          this.$message.error(`删除失败! (${err})`);
        });
      });
    },
    handleClose(done) {
      this.$confirm("确认关闭？")
        .then((_) => {
          this.dialog_show = false;
          this.initDialogData();
          done();
        })
        .catch((_) => {
        });
    },
  },
  created() {
    let query = this.$route.query;
    this.filters.limit = query.limit ? parseInt(query.limit) : 50;
    this.filters.page = query.page ? parseInt(query.page) : 1;
  },
  mounted() {
    this.get_data();
  },
  data() {
    return {
      filters: {
        page: 1,
        limit: 50,
      },
      items: [],
      total: 0,
      langs: {},
      loading: true,

      DIALOG_CREATION: "CREATION",
      DIALOG_EDIT: "EDIT",
      action: null,
      dialog_show: false,
      dialogRowId: null,
      canSubmit: true,
      dialogData: {
        contents: {},
      },
    }
  }
}
</script>
