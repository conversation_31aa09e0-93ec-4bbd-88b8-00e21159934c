<template>
  <div class="table-data">
    <el-row type="flex" justify="space-between" align="middle">
      <el-col :span="22">
        <h2 style="font-family: 'Microsoft YaHei',Arial,sans-serif;">
          生物识别验证详情
        </h2>
      </el-col>

      <el-col :span="1">
        <el-tooltip content="刷新" placement="right" :open-delay="500" :hide-after="2000">
          <el-button icon="el-icon-refresh-left" circle @click="get_data"></el-button>
        </el-tooltip>
      </el-col>
    </el-row>

    <h3 style="font-family: 'Microsoft YaHei',Arial,sans-serif;">
      审核信息
    </h3>
    <el-row type="flex" justify="space-between" align="top">
      <el-col :span="16">
        <el-form :model="form" label-width="100px" style="text-align: left;">
          <el-form-item label="用户ID:" style="margin-bottom: 0px;">
            <el-link :href="'/users/user-details?id=' + form.user_id" type="primary" target="_blank" :underline="false">
              {{ form.user_id }}
            </el-link>
          </el-form-item>
          <el-form-item label="用户邮箱:" style="margin-bottom: 0px;">
              {{ form.user_email }}
          </el-form-item>
          <el-form-item label="发送邮箱:" style="margin-bottom: 0px;">
              {{ form.email }}
          </el-form-item>
          <el-form-item label="创建时间:" style="margin-bottom: 0px;">
            <span v-if="form.created_at">
              {{ $formatDate(form.created_at) }}
            </span>
            <span v-else>
              -
            </span>
          </el-form-item>

          <el-form-item label="审核时间:" style="margin-bottom: 0px;">
            <span v-if="form.audited_at">
              {{ $formatDate(form.audited_at) }}
            </span>
            <span v-else>
              -
            </span>
          </el-form-item>

          <el-form-item label="审核类型:" style="margin-bottom: 0px;">
            {{ form.audit_type }}
          </el-form-item>

          <el-form-item label="审核状态:" style="margin-bottom: 0px;">
            {{ form.status }}
          </el-form-item>

          <el-form-item label="第三方拒绝理由:" style="width:500px;margin-bottom: 0px;">
            {{ form.third_reject_reason }}
          </el-form-item>

          <el-form-item label="人工拒绝理由:" style="width:500px;margin-bottom: 0px;">
            {{ form.admin_reject_reason }}
          </el-form-item>

          <el-form-item label="审核账号:" style="margin-bottom: 0px;">
            <span v-if="form.auditor_id">
              <el-link :href="'/users/user-details?id=' + form.auditor_id" type="primary" target="_blank" :underline="false">
                {{ form.auditor_email || form.auditor_id }}
              </el-link>
            </span>
            <span v-else>
              -
            </span>
          </el-form-item>

          <el-form-item label="备注:">
            <el-input
              v-model="form.remark"
              :disabled="disabled_edit_remark"
              type="textarea"
              :autosize="{ minRows: 1, maxRows: 10 }"
              placeholder="请输入"
              style="width: 320px;"
            ></el-input>
            <span v-if="disabled_edit_remark">
              <el-tooltip content="编辑备注" placement="right" :open-delay="500" :hide-after="2000">
                <el-button size="mini" type="primary" icon="el-icon-edit" circle
                  @click="disabled_edit_remark = false"></el-button>
              </el-tooltip>
            </span>
            <span v-else>
              <el-button @click="update_remark">保存</el-button>
            </span>
          </el-form-item>

          <h3 style="font-family: 'Microsoft YaHei',Arial,sans-serif;">
            生物识别验证信息
          </h3>

          <el-form-item label="人脸图片:">
            <span v-if="form.face_img_file_url">
              <el-tooltip content="镜像" placement="top" :open-delay="500" :hide-after="2000">
                <el-button icon="el-icon-back" circle @click="liveness_face_img_mir = !liveness_face_img_mir"></el-button>
              </el-tooltip>
              <el-tooltip content="旋转 π/2" placement="top" :open-delay="500" :hide-after="2000">
                <el-button icon="el-icon-refresh-left" circle @click="liveness_face_img_rot = (liveness_face_img_rot + 1) % 4"></el-button>
              </el-tooltip>
              <img
                :src="form.face_img_file_url"
                class="img"
                alt=""
                width="60%"
                :style="`transform: rotate(${liveness_face_img_rot * -90}deg) scaleX(${liveness_face_img_mir ? -1 : 1});`"
              />
            </span>
            <span v-else>
              -
            </span>
          </el-form-item>
        </el-form>
      </el-col>

      <el-col :span="8">
        <el-form :model="form">
          <span v-if="warning" style="color: red;"> 警告: {{ warning }} </span>

          <div>
            <el-switch v-model="form.is_custom_reason" active-text="自定义" inactive-text="拒绝原因">
            </el-switch>
            <el-form-item v-if="form.is_custom_reason === false">
              <el-select v-model="form.admin_reason" filterable style="width: 320px; margin-top: 20px;" multiple>
                <el-option v-for="(desc, reason) in form.extra.reason_dict" :key="reason" :label="desc" :value="reason"> </el-option>
              </el-select>
            </el-form-item>

            <el-form-item v-if="form.is_custom_reason === true">
              <el-input
                v-model="form.custom_admin_reason"
                placeholder="请输入"
                style="width: 320px;margin-top: 20px;"
                type="textarea"
                autosize
              ></el-input>
            </el-form-item>

            <h3>审核结果</h3>
            <el-form-item>
              <el-button type="success" @click="do_audit('PASSED')">
                审核通过
              </el-button>
              <el-button type="danger" @click="do_audit('REJECTED')">
                审核拒绝
              </el-button>
            </el-form-item>
          </div>

          <br />
          <h3>操作</h3>
          <el-form-item>
            <el-button type="primary" @click="go_to_next_pending">
              下一个待审核
            </el-button>
          </el-form-item>
          <el-form-item label="审核后自动跳转到下一个">
            <el-switch v-model="auto_next"></el-switch>
          </el-form-item>
        </el-form>
        <el-divider></el-divider>
        <el-row type="flex" justify="center" align="middle">
          <el-col :span="6">
            <h3>kyc信息</h3>
          </el-col>
          <el-col>
            <el-link
              :href="getKycDetailsUrl()"
              type="primary"
              :underline="false"
              target="_blank"
              style="width: 100%; font-weight: normal; display: inline-block; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
              跳转>
            </el-link>
          </el-col>
        </el-row>
        <el-form :model="form" label-width="120px" label-position="left">
          <el-row>
            <el-form-item label="居住国家:">
              {{ form.kyc.country_name }} ({{ form.kyc.country }})
            </el-form-item>
            <el-form-item label="国籍:">
              {{ form.kyc.nationality_name }} ({{ form.kyc.nationality }})
            </el-form-item>
            <el-form-item label="支持的证件类型:">
              <span v-for="v in form.kyc.supported_types">
                {{ form.kyc.id_type_dict[v] }}
              </span>
            </el-form-item>
          </el-row>

          <el-form-item label="姓名:">
            {{ form.kyc.full_name }}
          </el-form-item>
          <el-form-item label="性别:" v-if="form.kyc.gender && form.kyc.gender_dict">
            {{ form.kyc.gender_dict[form.kyc.gender] || '-' }}
          </el-form-item>
          <el-form-item label="出生日期:" v-if="form.kyc.date_of_birth">
            {{ form.kyc.date_of_birth }}
          </el-form-item>
          <el-form-item label="证件类型:">
            {{ form.kyc.id_type_dict && form.kyc.id_type_dict[form.kyc.id_type] || form.kyc.id_type || '-' }}
          </el-form-item>
          <el-form-item label="证件号码:">
            {{ form.kyc.id_number || '-' }}
          </el-form-item>
          <el-form-item label="用户填写的证件类型:" label-width="200px">
            {{ form.kyc.id_type_dict && form.kyc.id_type_dict[form.kyc.preset_id_type] || form.kyc.preset_id_type || '-' }}
          </el-form-item>
          <el-form-item label="用户填写的证件号码:" label-width="200px">
            {{ form.kyc.preset_id_number || '-' }}
          </el-form-item>
          <el-row v-if="form.kyc.doc_expire_date">
            <el-col :span="8">
              <el-form-item label="证件到期日:">
                {{ form.kyc.doc_expire_date }}
              </el-form-item>
            </el-col>
          </el-row>
          <el-form-item label="正面图片:">
            <span v-if="form.kyc.front_img_url">
              <el-tooltip content="镜像" placement="top" :open-delay="500" :hide-after="2000">
                <el-button icon="el-icon-back" circle @click="front_img_mir = !front_img_mir"></el-button>
              </el-tooltip>
              <el-tooltip content="旋转 π/2" placement="top" :open-delay="500" :hide-after="2000">
                <el-button icon="el-icon-refresh-left" circle
                  @click="front_img_rot = (front_img_rot + 1) % 4"></el-button>
              </el-tooltip>
              <img :src="form.kyc.front_img_url" class="img" alt="" width="60%"
                :style="`transform: rotate(${front_img_rot * -90}deg) scaleX(${front_img_mir ? -1 : 1});`">
            </span>
            <span v-else>
              -
            </span>
          </el-form-item>
          <el-form-item label="背面图片:">
            <span v-if="form.kyc.back_img_url">
              <el-tooltip content="镜像" placement="top" :open-delay="500" :hide-after="2000">
                <el-button icon="el-icon-back" circle @click="back_img_mir = !back_img_mir"></el-button>
              </el-tooltip>
              <el-tooltip content="旋转 π/2" placement="top" :open-delay="500" :hide-after="2000">
                <el-button icon="el-icon-refresh-left" circle
                  @click="back_img_rot = (back_img_rot + 1) % 4"></el-button>
              </el-tooltip>
              <img :src="form.kyc.back_img_url" class="img" alt="" width="60%"
                :style="`transform: rotate(${back_img_rot * -90}deg) scaleX(${back_img_mir ? -1 : 1});`">
            </span>
            <span v-else>
              -
            </span>
          </el-form-item>
          <el-form-item label="人像图片:">
            <span v-if="form.kyc.face_img_url">
              <el-tooltip content="镜像" placement="top" :open-delay="500" :hide-after="2000">
                <el-button icon="el-icon-back" circle @click="face_img_mir = !face_img_mir"></el-button>
              </el-tooltip>
              <el-tooltip content="旋转 π/2" placement="top" :open-delay="500" :hide-after="2000">
                <el-button icon="el-icon-refresh-left" circle
                  @click="face_img_rot = (face_img_rot + 1) % 4"></el-button>
              </el-tooltip>
              <img :src="form.kyc.face_img_url" class="img" alt="" width="60%"
                :style="`transform: rotate(${face_img_rot * -90}deg) scaleX(${face_img_mir ? -1 : 1});`">
            </span>
            <span v-else>
              -
            </span>
          </el-form-item>
        </el-form>
      </el-col>
    </el-row>

  </div>
</template>

<script>
import moment from 'moment';
import Vue from 'vue';
import VueClipboard from 'vue-clipboard2';

Vue.use(VueClipboard);

const list_url = '/api/operation/liveness-check/history';
const detail_url = '/api/operation/liveness-check';
const auditors_url = '/api/operation/liveness-check/auditors';

export default {
  watchQuery: true,
  asyncData({ app, query }) {
    let id = query.id;
    return app.$axios.get(`${detail_url}/${id}`).then(res => {
      return {
        form: res.data.data,
        loading: false,
        warning: '',
        auto_next: !!query.auto_next,
      };
    });
  },
  methods: {
    get_data() {
      let id = this.$route.query.id;
      this.loading = true;
      this.$axios.get(`${detail_url}/${id}`).then(res => {
        this.loading = false;
        if (res?.data?.code === 0) {
          Object.assign(this.form, res.data.data);
        } else {
          this.$message.error(`code: ${res.data?.code}; message: ${res.data?.message}`);
        }
      });
    },
    handle_review(row) {
      this.review_form = {
        id: row.id,
        remark: null,
      };
      this.review_dialog_show = true;
    },
    do_audit(value) {
      let title = `确认审核${value === 'REJECTED' ? '拒绝' : '通过'}?`;
      this.$confirm(title)
        .then(() => {
          let params = { status: value };
          if (value === 'REJECTED') {
            params['admin_reason'] = this.form.admin_reason || '';
            params['is_custom_reason'] = this.form.is_custom_reason;
            params['custom_admin_reason'] = this.form.custom_admin_reason || '';
            if (params['is_custom_reason'] && !params['custom_admin_reason']) {
              this.$message.error('自定义原因未填写');
              return;
            }
          }
          this.$axios
            .put(`${detail_url}/${this.$route.query.id}`, params)
            .then(res => {
              if (res?.data?.code === 0) {
                this.$message.success('操作成功!');
                this.get_data();
                if (this.auto_next) {
                  this.go_to_next_pending();
                }
              } else {
                this.$message.error(`操作失败! (code: ${res.data?.code}; message: ${res.data?.message}; data: ${res.data?.data});`);
              }
            })
            .catch(err => {
              this.$message.error(`操作失败! (${err})`);
            });
          done();
        })
        .catch(() => {});
    },
    go_to_next_pending() {
      this.$axios
        .get(list_url, { params: { start_id: this.$route.query.id } })
        .then(res => {
          if (res?.data?.code === 0) {
            let id = parseInt(this.$route.query.id);
            let next_id = null;
            res.data.data.items.forEach(item => {
              if (String(item.id) !== String(id)) {
                next_id = item.id;
              }
            });
            if (next_id !== null) {
              this.$router.push({ path: this.$route.path, query: { id: next_id, auto_next: this.auto_next ? 1 : 0 } });
            } else {
              this.$message.success(`没有下一个`);
            }
          } else {
            this.$message.error(`请求失败! (code: ${res.data?.code}; message: ${res.data?.message})`);
          }
        })
        .catch(err => {
          this.$message.error(`请求失败! (${err})`);
        });
    },
    auto_refresh() {
      if (this.form.status !== 'AUDIT_REQUIRED') {
        return;
      }
      const id = this.$route.query.id;
      if (!id) {
        return;
      }
      this.$axios.post(`${auditors_url}/${id}`).then(res => {
        this.warning = '';
        if (res?.data?.code === 0) {
          let data = res.data.data;
          if (data.status === 'AUDIT_REQUIRED') {
            let text = Object.values(data.other_auditors).join(', ');
            if (text) {
              this.warning = `${text} 也在操作!`;
            }
          }
        }
      });
    },
    format_date(timestamp, pattern = 'YYYY-MM-DD HH:mm:ss') {
      return moment(Number(timestamp) * 1000).format(pattern);
    },
    handle_content_copy() {
      this.$message.success('内容已复制到剪贴板');
    },
    update_remark() {
      this.$axios
        .patch(`/api/operation/liveness-check/${this.form.id}`, {
          remark: this.form.remark,
        })
        .then((res) => {
          if (res?.data?.code === 0) {
            this.get_data();
            this.disabled_edit_remark = true;
          } else {
            this.$message.error(`code: ${res.data?.code}; message: ${res.data?.message}`);
          }
        })
        .catch((e) => {
          this.$message.error(e);
        });
    },
      getKycDetailsUrl() {
        if (this.form.kyc.kyc_type === 'INDIVIDUAL') {
          return '/operation/kyc-details/?id=' + this.form.kyc_id;
        } else if (this.form.kyc.kyc_type === 'INSTITUTION') {
          return '/operation/kyc-institution-detail/?id=' + this.form.kyc_id;
        } else {
          return '/operation/kyc-details/?id=' + this.form.kyc_id;
        }
      },
  },
  mounted() {
    this.auto_next = !!this.$route.query.auto_next;
    // this.auto_refresh_timer = setInterval(this.auto_refresh, 5000);
  },
  data() {
    return {
      form: {
        user_id: null,
        user_name: null,
        user_remark: null,
        created_at: null,
        auditor_id: null,
        auditor_name: null,
        status: null,
        status_name: null,
        audit_type: null,
        admin_reason: null,
        custom_admin_reason: null,
        is_custom_reason: false,
        kyc: {},
        extra: {},
      },
      review_form: {
        id: null,
        remark: null,
      },
      review_dialog_show: false,
      loading: false,
      warning: '',
      auto_next: false,
      auto_refresh_timer: null,
      status_dict: {},
      front_img_rot: 0,
      front_img_mir: false,
      back_img_rot: 0,
      back_img_mir: false,
      face_img_rot: 0,
      face_img_mir: false,
      liveness_face_img_rot: 0,
      liveness_face_img_mir: false,
      disabled_edit_remark: true,
    };
  },
  beforeDestroy() {
    clearInterval(this.auto_refresh_timer);
  },
};
</script>

<style>
.img-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}
.img-uploader .el-upload:hover {
  border-color: #409eff;
}
.img-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}
.el-form-item__label {
  text-align: left;
}
</style>
