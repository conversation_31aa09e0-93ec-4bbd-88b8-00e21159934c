<template>
    <div class="table-data">
      <h2 style="font-family: 'Microsoft YaHei',Arial,sans-serif;">
        直播封面管理
      </h2>

      <el-button type="primary" @click="create_live_page()">新增封面</el-button>
  
      <el-table v-loading="loading"
                :data="items"
                stripe>
  
        <el-table-column
          label="封面ID"
          prop="id">
        </el-table-column>

        <el-table-column
            prop="name"
            label="封面名称">
        </el-table-column>

        <el-table-column
          prop="live_page_url"
          label="生产链接">
        </el-table-column>

        <el-table-column
          prop="assets"
          label="展示币种"
          >
          <template v-slot="scope">
            {{ scope.row.assets && scope.row.assets.join(', ') }}
            </template>
        </el-table-column>

        <el-table-column
          label="右下角内容">
          <template v-slot="scope">
            {{ scope.row.is_qr_code ? 'QR Code' : '海报' }}
          </template>
        </el-table-column>

        <el-table-column
          label="生成时间"
          prop="created_at">
        </el-table-column>

        <el-table-column label="操作人" show-overflow-tooltip>
            <template slot-scope="scope">
                <el-link :href="'/users/user-details?id=' + scope.row.operator" type="primary"
                    target="_blank" :underline="false"
                    style="width: 100%; font-weight: normal; display: inline-block; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
                    {{ scope.row.operator_name }}
                </el-link>
            </template>
        </el-table-column>

        <el-table-column label="操作">
            <template slot-scope="scope">
            <el-tooltip content="编辑" placement="right" :open-delay="500" :hide-after="2000">
                <el-button size="mini" icon="el-icon-edit" circle @click="edit_live_page(scope.row)"></el-button>
            </el-tooltip>

            <el-tooltip content="删除" placement="right" :open-delay="500" :hide-after="2000">
                <el-button size="mini" type="danger" icon="el-icon-delete" circle @click="delete_live_page(scope.row)"></el-button>
            </el-tooltip>
            </template>
        </el-table-column>
  
      </el-table>
      <el-pagination
            :current-page.sync="search_data.page"
            :page-size.sync="search_data.limit"
            :page-sizes="[10, 50, 100]"
            @current-change="get_data(false)"
            :hide-on-single-page="true"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
        >
        </el-pagination>
  
      <el-backtop></el-backtop>

      <el-dialog :title="livePageInfo.uid ? '编辑直播封面' : '新增直播封面'" :visible.sync="showLivePageDialog" width="80%" :before-close="closeLivePageDialog">
        <el-form :model="livePageInfo">
          <el-form-item label="封面名称" required>
            <el-input v-model="livePageInfo.name" style="width: 80%;"/>
          </el-form-item>

          <el-form-item label="展示币种" required>
            <el-select v-model="livePageInfo.assets"
                    class="filter-paste"
                    clearable
                    filterable
                    :disabled="!assets"
                    :filter-method="left_prefix_match_asset"
                    style="width: 80%;"
                    multiple                 
                    :multiple-limit="5"      
                    placeholder="多选, 需配置5个币种">
                <el-option v-for="asset in asset_list"
                            :key="asset"
                            :label="asset"
                            :value="asset">
                </el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="右下角内容" required>
            <el-radio-group v-model="livePageInfo.is_qr_code">
                <el-radio :label="true">QR Code</el-radio>
                <el-radio :label="false">海报</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item v-if="!livePageInfo.is_qr_code">
            <el-upload
                ref="upload-icon"
                action="/api/upload/image"
                name="img"
                accept=".jpg, .jpeg, .png, .webp"
                :data="{ img_size: '460,219' }"
                :on-success="handleUploadSuccess"
                :on-error="handleUploadError"
                :auto-upload="true"
                :show-file-list="false"
                :headers="{'AUTHORIZATION': $cookies.get('admin_token')}"
            >
                <el-button type="primary" :disabled="livePageInfo.poster_files.length >= 5">选择文件</el-button>
                <span class="upload-tip">
                    尺寸：460*219，格式jpg png webp
                </span>
            </el-upload>

            <div v-if="livePageInfo.poster_files.length > 0" class="uploaded-files-list">
                <div v-for="file in livePageInfo.poster_files" :key="file.file_id" class="uploaded-file-item">
                    <a href="javascript:void(0)" @click="showImagePreview(file.file_url)" class="file-link">{{ file.file_name }}</a>
                    <el-button
                    @click="removeFile(file)"
                    type="danger"
                    icon="el-icon-delete"
                    size="mini"
                    circle
                    ></el-button>
                </div>
            </div>
          </el-form-item>

          <el-form-item label="宣传语">
            <el-input v-model="livePageInfo.slogan" placeholder="请输入，默认二维码时引导下载，海报时slogan" />
          </el-form-item>

        </el-form>
        <template #footer>
          <el-button @click="closeLivePageDialog()">取消</el-button>
          <el-button type="primary" @click="saveLivePage()">保存</el-button>
        </template>
      </el-dialog>

      <el-dialog :visible.sync="previewDialogVisible" title="图片预览" width="60%">
        <img :src="previewImageUrl" style="max-width: 100%; display: block; margin: 0 auto;">
      </el-dialog>

    </div>
  </template>
  
  <script>
    export default {
      methods: {
        get_data() {
            this.loading = true;
            this.$axios.get(
                '/api/live/list', {
                    params: this.search_data
                }
            ).then(
                res => {
                if (res.data.code === 0) {
                    this.items = res.data.data.items;
                    this.total = res.data.data.total;
                    this.loading = false;
                } else {
                    this.$message.error(`code: ${res.data?.code}; message: ${res.data?.message}`);
                }
                }
            ).catch(err => {
                this.$message.error(`刷新失败! (${err})`);
            });
        },
        get_all_assets() {
            this.$axios.get('/api/asset/assets').then(res => {
            if (res?.data?.code === 0) {
                this.assets = res.data.data;
                this.update_asset_list();
            }
            });
        },
        left_prefix_match_asset(val){
            this.update_asset_list();
            if (val){
                this.asset_list = this.left_prefix_match(val, this.assets);
            }
        },
        update_asset_list() {
            this.asset_list = this.assets;
        },
        left_prefix_match(val, source){
            val = val.toUpperCase(val);
            let match_list = [];
            if(val){
            for (const sc of source){
                if (sc.startsWith(val)){
                if (sc == val){
                    match_list.unshift(sc);
                }else{
                    match_list.push(sc)
                }
                }
            }
            return match_list
            }
        },
        create_live_page() {
            this.get_all_assets();
            this.livePageInfo = this.defaultLivePageInfo();
            this.showLivePageDialog = true
        },
        edit_live_page(row) {
            this.get_all_assets();
            this.$axios.get(
                '/api/live/', {
                    params: {
                        'uid': row.uid,
                    }
                }
            ).then(
                res => {
                if (res.data.code === 0) {
                    this.livePageInfo.uid = res.data.data.uid;
                    this.livePageInfo.name = res.data.data.name;
                    this.livePageInfo.assets = res.data.data.assets;
                    this.livePageInfo.is_qr_code = res.data.data.is_qr_code;
                    this.livePageInfo.poster_files = res.data.data.poster_files;
                    this.livePageInfo.poster_file_ids = res.data.data.poster_file_ids;
                    this.livePageInfo.slogan = res.data.data.slogan;
                } else {
                    this.$message.error(`code: ${res.data?.code}; message: ${res.data?.message}`);
                }
                }
            ).catch(err => {
                this.$message.error(`刷新失败! (${err})`);
            });
            this.showLivePageDialog = true
        },
        delete_live_page(row) {
            this.$confirm(`确认删除 ${row.name}?`).then(() => {
                this.loading = true
                this.$axios.delete(
                    `/api/live/`, {params: {'uid': row.uid} }
                ).then(
                res => {
                    this.get_data();
                    if (res.data.code === 0) {
                        this.$message.success("删除成功!");
                        this.get_data();
                        this.loading = false;
                    } else {
                    this.$message.error(`code: ${res.data?.code}; message: ${res.data?.message}`);
                    }
                }
                ).catch(err => {
                    this.$message.error(`失败! (${err})`);
                });
            });
        },
        closeLivePageDialog() {
            this.showLivePageDialog = false
            this.livePageInfo = this.defaultLivePageInfo();
        },
        saveLivePage() {
            if (this.livePageInfo.assets.length !== 5) {
                this.$message.error('请检查，展示币种固定配置数量为5个');
                return
            }

            if (!this.livePageInfo.is_qr_code && this.livePageInfo.poster_file_ids.length === 0) {
                this.$message.error('请检查，海报类型需上传文件');
                return
            }

            this.$axios.post(
                '/api/live/',
                this.livePageInfo
            ).then(
                res => {
                if (res.data.code === 0) {
                    this.get_data();
                    this.res_success_notice(res);
                } else {
                    this.res_fail_notice(res);
                }
                }
            ).catch(err => {
                this.res_error_notice(err);
            });
        },
        handleUploadSuccess(response) {
            if (response.code === 0) {
                this.livePageInfo.poster_file_ids.push(response.data.file_id)
                this.livePageInfo.poster_files.push({
                    file_id: response.data.file_id,
                    file_name: response.data.file_name,
                    file_url: response.data.file_url,
                })
                this.$message.success(`上传成功`);
                this.get_data();
            } else {
                this.$message.error(`上传失败: ${response.message}`);
            }
        },
        handleUploadError(err) {
            this.$message.error(`上传失败: ${err}`);
        },
        removeFile(file) {
            this.livePageInfo.poster_files = this.livePageInfo.poster_files.filter(
                item => item.file_id !== file.file_id);
            this.livePageInfo.poster_file_ids = this.livePageInfo.poster_file_ids.filter(
                id => id !== file.file_id
            );
        },
        notice(title, message) {
            this.$alert(message, title, {
                confirmButtonText: '确定',
            });
        },
        res_success_notice(r) {
            let title = "保存成功";
            this.notice(title, title);
        },
        res_fail_notice(res) {
            let title = "保存失败";
            let message = `(code: ${res.data?.code}; message: ${res.data?.message})`;
            this.notice(title, message);
        },
        res_error_notice(error) {
            let title = "保存失败";
            let message = `(code: ${error.response.status}; message: ${error.message})`;
            this.notice(title, message);
        },
        showImagePreview(url) {
            this.previewImageUrl = url;
            this.previewDialogVisible = true;
        },
      },
      mounted() {
        this.get_data();
        this.get_all_assets();
      },
      data() {
        
        return {
            showLivePageDialog: false,
            items: [],
            total: 0,
            assets: [],
            asset_list: [],
            livePageInfo: {
                uid: null,
                name: null,
                assets: [],
                is_qr_code: true,
                poster_file_ids: [],
                poster_files: [], 
                slogan: null,
            },
            defaultLivePageInfo: () => ({
                uid: null,
                name: null,
                assets: [],
                is_qr_code: true,
                poster_file_ids: [],
                poster_files: [], 
                slogan: null,
            }),
            search_data: {
                page: 1,
                limit: 50,
            },
            previewDialogVisible: false,
            previewImageUrl: '',
        }
      }
    }
  </script>
  

<style scoped>
/* 自定义上传列表的样式 */
.uploaded-files-list {
  margin-top: 15px;
  width: 100%;
}
.uploaded-file-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
  margin-bottom: 8px;
}
.file-link {
  color: #409eff;
  text-decoration: none;
  font-size: 14px;
  flex-grow: 1; /* 占据剩余空间 */
}
.file-link:hover {
  text-decoration: underline;
}
</style>