<template>
  <el-container>
    <el-main>
      <h2 style="font-family: 'Microsoft YaHei', <PERSON><PERSON>, sans-serif">商务-变更记录</h2>

      <el-form :inline="true" :model="search_data">
        <template>
          <el-form-item prop="start_time" label="开始时间">
            <el-date-picker
              required
              v-model="search_data.start_time"
              type="datetime"
              value-format="timestamp"
              :picker-options="pickerOptions"
              placeholder="开始时间"
              @change="search"
            ></el-date-picker>
          </el-form-item>
          <el-form-item prop="end_time" label="结束时间">
            <el-date-picker
              required
              v-model="search_data.end_time"
              type="datetime"
              value-format="timestamp"
              :picker-options="pickerOptions"
              placeholder="结束时间"
              @change="search"
            ></el-date-picker>
          </el-form-item>
        </template>

        <el-form-item label="用户搜索">
          <UserSearch v-model="search_data.user_id" :refresh_method="search" @change="search"></UserSearch>
        </el-form-item>

        <el-form-item label="团队ID/商务ID">
          <el-input clearable v-model="search_data.biz_id" placeholder @change="search"></el-input>
        </el-form-item>

        <el-form-item label="变更类型">
          <el-select
            clearable
            filterable
            v-model="search_data.change_type"
            placeholder="<ALL>"
            @change="search"
          >
            <el-option
              v-for="(value, label) in change_type_dict"
              :key="value"
              :label="value"
              :value="label"
            ></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="操作人">
          <UserSearch v-model="search_data.admin_user_id" :refresh_method="search" @change="search"></UserSearch>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="search">查询</el-button>
        </el-form-item>
      </el-form>

      <el-table :data="items" style="width: 100%">
        <el-table-column
          label="时间"
          prop="created_at"
          :formatter="row => $formatDate(row.created_at, 'YYYY-MM-DD HH:mm:ss')"
        ></el-table-column>

        <el-table-column prop="biz_id" label="团队ID/商务ID"></el-table-column>

        <el-table-column label="变更类型" prop="change_type">
          <template slot-scope="scope">{{ change_type_dict[scope.row.change_type] }}</template>
        </el-table-column>

        <el-table-column label="变更详情" prop="detail_str"></el-table-column>

        <el-table-column prop="admin_name" label="操作人">
          <template slot-scope="scope">
            <el-link
              v-if="scope.row.admin_user_id"
              :href="'/users/user-details?id=' + scope.row.admin_user_id"
              type="primary"
              target="_blank"
              :underline="false"
              style="
                width: 100%;
                font-weight: normal;
                display: inline-block;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
              "
            >{{ scope.row.admin_user_name }}</el-link>
            <span v-else>系统</span>
          </template>
        </el-table-column>
      </el-table>

      <el-pagination
        :current-page.sync="search_data.page"
        :page-size.sync="search_data.limit"
        :total="total"
        @current-change="search"
        :page-sizes="[50, 100, 200]"
        layout="total, sizes, prev, pager, next, jumper"
      ></el-pagination>
      <el-backtop></el-backtop>
    </el-main>
  </el-container>
</template>

<script>
import moment from "moment";
import UserSearch from "../../../components/user/UserSearch";
const binding_url = "/api/bus-amb/change-log";

export default {
  components: { UserSearch },
  methods: {
    search(reset = true) {
      if (reset === true) {
        this.search_data.page = 1;
      }
      if (this.search_data.biz_id === "") {
        this.search_data.biz_id = null;
      }
      this.$router.push({ path: this.$route.path, query: this.search_data });
      this.$axios
        .get(binding_url, { params: this.search_data })
        .then((res) => {
          this.total = res.data.data.total;
          this.items = res.data.data.items;
          this.change_type_dict = res.data.data.extra.change_type_dict;
        })
        .catch((e) => {});
    },
    update_router_query() {
      let query = {};
      Object.assign(query, this.search_data);
      Object.keys(query).forEach((key) => !query[key] && delete query[key]);
      this.$router.replace({ query: query });
    },
  },
  created() {
    let query = this.$route.query;
    let search_data = this.search_data;
    search_data.limit = query.limit ? parseInt(query.limit) : 50;
    search_data.page = query.page ? parseInt(query.page) : 1;
    this.$watch("search_data", {
      handler: function () {
        this.update_router_query();
      },
      deep: true,
    });
  },
  mounted() {
    this.search();
  },
  data() {
    return {
      search_data: {
        limit: 50,
      },
      items: [],
      total: 0,
      change_type_dict: {},
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now();
        },
      },
    };
  },
};
</script>
