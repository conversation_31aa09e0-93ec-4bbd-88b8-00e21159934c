<template>
  <el-container>
    <el-main>
      <h2 style="font-family: 'Microsoft YaHei', Arial, sans-serif">
        子代理列表
      </h2>

      <el-row> 数量：{{ total }} </el-row>
      <br />

      <el-form :inline="true">
        <el-form-item label="状态" :model="search_data">
          <el-select v-model="search_data.status" @change="get_data" placeholder="<ALL>">
            <el-option v-for="(name, key) in status_dict" :key="key" :label="name" :value="key">
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item>
          <UserSearch v-model="search_data.user_id" @change="get_data"></UserSearch>
        </el-form-item>

        <el-form-item label="上级用户">
          <UserSearch v-model="search_data.parent_id" @change="get_data"></UserSearch>
        </el-form-item>

        <el-form-item label="所属商务">
          <UserSearch v-model="search_data.bus_user_id" @change="get_data"></UserSearch>
        </el-form-item>

        <el-form-item label="所属团队">
          <el-select v-model="search_data.team_id" @change="get_data" placeholder="<ALL>" filterable clearable>
            <el-option v-for="(name, key) in team_name_map" :key="key" :label="name" :value="key">
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="代理级别">
          <el-input clearable v-model="search_data.tree_height" placeholder @change="get_data"></el-input>
        </el-form-item>

        <el-form-item>
          <el-tooltip content="下载" placement="right" :open-delay="500" :hide-after="2000">
            <el-button icon="el-icon-download" circle type="success" @click="download"></el-button>
          </el-tooltip>
        </el-form-item>

        <el-form-item>
          <el-tooltip content="刷新" placement="right" :open-delay="500" :hide-after="2000">
            <el-button icon="el-icon-refresh-left" circle @click="get_data"></el-button>
          </el-tooltip>
        </el-form-item>

      </el-form>

      <el-table :data="items" v-loading="loading" width="100%" stripe border @sort-change="handle_sort">
        <el-table-column prop="user_id" label="用户ID" show-overflow-tooltip fixed min-width="120px">
          <template slot-scope="scope">
            <el-link :href="scope.row.user_id | user_link" type="primary" target="_blank" :underline="false" style="
                width: 100%;
                font-weight: normal;
                display: inline-block;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
              ">
              {{ scope.row.user_id }}
            </el-link>
          </template>
        </el-table-column>

        <el-table-column show-overflow-tooltip prop="email" label="邮箱" fixed min-width="300px">
          <template slot-scope="scope">
            <el-link :href="scope.row.user_id | user_link" type="primary" target="_blank" :underline="false" style="
                width: 100%;
                font-weight: normal;
                display: inline-block;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
              ">
              {{ scope.row.email }}
            </el-link>
            <el-col :span="2">
              <el-tooltip content="复制" placement="top" :open-delay="500" :hide-after="2000">
                <el-button icon="el-icon-document-copy" size="mini" circle v-clipboard:copy="scope.row.email"
                  v-clipboard:success="handle_content_copy"></el-button>
              </el-tooltip>
            </el-col>
          </template>
        </el-table-column>

        <el-table-column prop="status" label="状态" :formatter="(row) => status_dict[row.status]" show-overflow-tooltip
          min-width="100px">
        </el-table-column>

        <el-table-column prop="rate" label="返佣比例" show-overflow-tooltip min-width="120px">
        </el-table-column>

        <el-table-column prop="allow_add_child" label="是否允许拓展子代理" show-overflow-tooltip min-width="150px">
          <template slot-scope="scope">
            {{ scope.row.allow_add_child ? '是' : '否' }}
          </template>
        </el-table-column>

        <el-table-column prop="bus_user_id" label="所属商务" show-overflow-tooltip min-width="300px">
          <template slot-scope="scope">
            <el-link :href="scope.row.bus_user_id | user_link" type="primary" target="_blank" :underline="false" style="
                width: 100%;
                font-weight: normal;
                display: inline-block;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
              ">
              {{ scope.row.bus_name || scope.row.bus_user_id }}
            </el-link>
          </template>
        </el-table-column>

        <el-table-column prop="team_name" label="所属团队" show-overflow-tooltip min-width="200px">
        </el-table-column>

        <el-table-column prop="tree_height" label="代理级别" show-overflow-tooltip min-width="100px">
          <template slot-scope="scope">
            {{ scope.row.tree_height }} 级
          </template>
        </el-table-column>

        <el-table-column prop="tree_paths" label="代理链路" show-overflow-tooltip min-width="300px">
          <template slot-scope="scope">
            <div>
              <!-- 展示前3项 + 省略号 -->
              <el-popover placement="right" trigger="hover" width="400" popper-class="full-path-popover">
                <!-- 全部内容放在 popover slot -->
                <div v-for="(path, index) in scope.row.tree_paths" :key="'all_' + index"
                  style="display: flex; align-items: center; margin-bottom: 4px;">
                  <el-link :href="path[2] | user_link" target="_blank" type="primary" :underline="false"
                    style="margin-right: 5px; width: 300px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap; display: inline-block;">
                    {{ path[1] }}: {{ path[3] }}
                  </el-link>
                  <span
                    style="font-weight: bold; color: #3498db; background-color: #f0f0f0; padding: 2px 5px; border-radius: 3px;">
                    {{ path[4] }}
                  </span>
                </div>

                <!-- 触发内容 -->
                <div slot="reference">
                  <div v-for="(path, index) in scope.row.tree_paths.slice(0, 3)" :key="'short_' + index"
                    style="display: flex; align-items: center;">
                    <el-link :href="path[2] | user_link" target="_blank" type="primary" :underline="false"
                      style="margin-right: 5px; width: 300px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap; display: inline-block;">
                      {{ path[1] }}: {{ path[3] }}
                    </el-link>
                    <span
                      style="font-weight: bold; color: #3498db; background-color: #f0f0f0; padding: 2px 5px; border-radius: 3px;">
                      {{ path[4] }}
                    </span>
                  </div>
                  <!-- 超出提示 -->
                  <div v-if="scope.row.tree_paths.length > 3" style="color: #999;">...更多</div>
                </div>
              </el-popover>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="effected_at" label="成为代理时间" :formatter="(row) => $formatDate(row.effected_at)"
          show-overflow-tooltip min-width="200px">
        </el-table-column>

        <el-table-column prop="effect_refer_count" label="refer人数" show-overflow-tooltip min-width="120px">
        </el-table-column>

        <el-table-column prop="effect_refer_trade_count" label="refer交易人数" show-overflow-tooltip min-width="120px">
        </el-table-column>

        <el-table-column prop="this_month_deal_amount" label="当月refer交易总额（USD）" show-overflow-tooltip
          min-width="200px"></el-table-column>

        <el-table-column prop="effect_refer_trade_amount" label="refer交易总额（USD）" show-overflow-tooltip
          min-width="200px"></el-table-column>

        <el-table-column prop="month_refer_amount" label="当月返佣（USDT）" show-overflow-tooltip
          min-width="160px"></el-table-column>

        <el-table-column prop="total_refer_amount" label="累计返佣（USDT）" show-overflow-tooltip min-width="160px">
        </el-table-column>

        <el-table-column prop="admin_remark" label="备注" show-overflow-tooltip min-width="120px">
        </el-table-column>

        <el-table-column prop="operation" min-width="120px" label="操作">
          <template slot-scope="scope">
            <el-tooltip content="编辑" placement="right" :open-delay="500" :hide-after="2000">
              <el-button size="mini" type="primary" icon="el-icon-edit" circle
                @click="handleEdit(scope.row)"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="right" :open-delay="500" :hide-after="2000">
              <el-button size="mini" type="danger" icon="el-icon-delete" circle
                @click="handleDelete(scope.row)"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <el-pagination :current-page.sync="search_data.page" :page-size.sync="search_data.limit" @size-change="get_data"
        @current-change="get_data" :page-sizes="[50, 100]" :hide-on-single-page="true"
        layout="total, sizes, prev, pager, next, jumper" :total="total">
      </el-pagination>

      <el-dialog :title="action === DIALOG_CREATION ? '添加子代理' : '编辑子代理'" :visible.sync="dialog_show" width="70%">
        <el-form :model="submit_data" ref="submit_data" label-width="150px" :validate-on-rule-change="false">
          <el-form-item prop="user_id" label="用户" required>
            <el-input v-model="submit_data.user_id" style="width: 300px" disabled></el-input>
          </el-form-item>

          <el-form-item label="返佣比例" prop="rate" required>
            <el-input-number v-model="submit_data.rate" style="width: 300px" :max="1" :min="0" :precision="4"
              :step="0.01"></el-input-number>
          </el-form-item>

          <el-form-item prop="allow_add_child" label="是否允许拓展子代理" required>
            <el-radio-group v-model="submit_data.allow_add_child" style="width: 300px">
              <el-radio :label="true">是</el-radio>
              <el-radio :label="false">否</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item prop="admin_remark" label="备注">
            <el-input v-model="submit_data.admin_remark" style="width: 80%"></el-input>
          </el-form-item>
        </el-form>
        <span slot="footer" class="dialog-footer">
          <el-button type="primary" @click="submit">确 定</el-button>
        </span>
      </el-dialog>

      <el-backtop></el-backtop>
    </el-main>
  </el-container>
</template>

<script>
import UserSearch from "../../../components/user/UserSearch";
import _ from "lodash";
import Vue from "vue";
import VueClipboard from "vue-clipboard2";

const amb_list_url = "/api/bus-amb/tree-ambassadors";
const export_url = "/api/bus-amb/tree-ambassadors/export";

Vue.use(VueClipboard);
export default {
  components: { UserSearch },
  methods: {
    get_data(sort_col = null, sort_type = null) {
      this.dialog_show = false;
      this.loading = true;
      let new_query = _.omitBy(this.search_data, (value, _) => value === undefined || value === null || value === "")
      if (sort_col && sort_type) {
        new_query.sort_col = sort_col
        new_query.sort_type = sort_type
      }
      this.$axios
        .get(amb_list_url, { params: new_query })
        .then((res) => {
          if (res.data.code === 0) {
            let data = res.data.data;
            this.items = data.items;
            this.total = data.total;
            this.status_dict = data.extra.status_dict;
            this.team_name_map = data.extra.team_name_map;
            this.loading = false;
          } else {
            this.$message.error(
              `code: ${res.data?.code}; message: ${res.data?.message}; data: ${res.data?.data}`
            );
          }
        })
        .catch((err) => {
          this.$message.error(`刷新失败! (${err})`);
        });
    },
    handle_content_copy() {
      this.$message.success("内容已复制到剪贴板");
    },
    handle_sort(event) {
      let { _, order, prop } = event;
      this.get_data(prop, order);
    },
    download() {
      let params = { ...this.search_data, export: 1 };
      delete params["page"];
      delete params["limit"];
      this.$axios
        .get(export_url, { params: params, responseType: "blob" })
        .then((res) => {
          const content_type = res.headers["content-type"];
          const url = window.URL.createObjectURL(
            new Blob([res.data], content_type ? { type: content_type } : {})
          );
          const a = document.createElement("a");
          a.href = url;
          document.body.appendChild(a);
          const content_disposition = res.headers["content-disposition"];
          a.download = content_disposition
            ? content_disposition.split("filename=")[1]
            : "temp.xlsx";
          a.click();
          window.URL.revokeObjectURL(url);
        });
    },
    handleEdit(row) {
      this.action = this.DIALOG_EDIT;
      this.submit_data = _.clone(row);
      this.dialog_show = true;
    },
    handleDelete(row) {
      this.$confirm(`确认删除?`).then(() => {
        this.$axios
          .delete(amb_list_url, {
            params: { user_id: row.user_id },
          })
          .then((res) => {
            if (res?.data?.code === 0) {
              this.get_data();
              this.$message.success("删除成功!");
            } else {
              this.$message.error(
                `删除失败! (code: ${res.data?.code}; message: ${res.data?.message}; data: ${res.data?.data}`
              );
            }
          })
          .catch((err) => {
            this.$message.error(`删除失败! (${err})`);
          });
      });
    },
    handleCreateConfirm() {
      this.$axios
        .post(amb_list_url, this.submit_data)
        .then((res) => {
          this.get_data();
          if (res.data.code === 0) {
            this.$message.success("提交成功!");
            this.loading = false;
            this.dialog_show = false;
          } else {
            this.$message.error(
              `code: ${res.data?.code}; message: ${res.data?.message}; data: ${res.data?.data}`
            );
          }
        })
        .catch((err) => {
          this.$message.error(`失败! (${err})`);
        });
    },
    handleEditConfirm() {
      this.$axios
        .put(amb_list_url, this.submit_data)
        .then((res) => {
          if (res.data.code === 0) {
            this.get_data();
            this.$message.success("提交成功!");
            this.loading = false;
            this.dialog_show = false;
          } else {
            this.$message.error(
              `code: ${res.data?.code}; message: ${res.data?.message}; data: ${res.data?.data}`
            );
          }
        })
        .catch((err) => {
          this.$message.error(`失败! (${err})`);
        });
    },
    submit() {
      this.$refs["submit_data"].validate((valid) => {
        if (!valid) {
          this.$alert("校验失败请修改", "校验失败请修改", {
            confirmButtonText: "确定",
          });
        } else {
          if (this.action === this.DIALOG_CREATION) {
            this.handleCreateConfirm();
          } else {
            this.handleEditConfirm();
          }
        }
      });
    },
  },
  mounted() {
    let bus_user_id = this.$route.query.bus_user_id;
    if (bus_user_id !== undefined) {
      this.bus_user_id = bus_user_id;
      this.search_data.bus_user_id = bus_user_id;
    }
    this.get_data();
  },
  created() {
    this.$sync_router_query(this, "search_data", {
      status: String,
      user_id: Number,
      bus_user_id: Number,
      parent_id: Number,
      team_id: Number,
      tree_height: Number,
    });
  },
  computed: {
    headers() {
      return {
        'AUTHORIZATION': this.$cookies.get('admin_token'),
      }
    },
  },
  data() {
    return {
      show: false,
      action: null,
      DIALOG_CREATION: "CREATION",
      DIALOG_EDIT: "EDIT",
      items: [],
      loading: true,
      dialog_show: false,
      cur_row: null,
      submit_data: {
        user_id: null,
        rate: null,
        allow_add_child: null,
        admin_remark: null,
      },
      search_data: {
        status: "VALID",
        user_id: null,
        bus_user_id: null,
        page: 1,
        limit: 100,
      },
      total: 0,
      status_dict: {},
      team_name_map: {},
    };
  },
};
</script>

<style>
.disable .el-upload--picture-card {
  display: none;
}
</style>
