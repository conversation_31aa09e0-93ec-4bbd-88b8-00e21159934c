<template>
  <div class="test-features">
    <h2>功能测试开关</h2>
    <el-table :data="featureToggles" style="width: 100%">
      <el-table-column prop="name" label="功能名称" width="180">
      </el-table-column>
      <el-table-column prop="description" label="功能描述">
      </el-table-column>
      <el-table-column prop="cookieKey" label="Cookie键名">
      </el-table-column>
      <el-table-column label="操作" width="120">
        <template slot-scope="scope">
          <el-button
            :type="scope.row.enabled ? 'danger' : 'success'"
            size="small"
            @click="toggleFeature(scope.row)"
          >
            {{ scope.row.enabled ? '关闭' : '开启' }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
export default {
  name: 'TestFeatures',
  data() {
    return {
      featureToggles: [
        {
          name: '页面浏览收藏',
          description: '启用页面浏览收藏功能',
          cookieKey: 'ENABLE_NAV_PAGE',
          enabled: false
        },
        // 可以在这里添加更多功能开关
      ]
    }
  },
  mounted() {
    // 初始化时检查每个功能的cookie状态
    this.featureToggles = this.featureToggles.map(toggle => ({
      ...toggle,
      enabled: !!this.$cookies.get(toggle.cookieKey)
    }))
  },
  methods: {
    async toggleFeature(feature) {
      if (feature.enabled) {
        // 关闭功能 - 删除cookie
        this.$cookies.remove(feature.cookieKey)
        feature.enabled = false
      } else {
        // 开启功能 - 设置cookie
        this.$cookies.set(feature.cookieKey, 'true')
        feature.enabled = true
      }

      // 触发全局状态更新
      this.$store.commit('user/enable_refresh_nav')
      
      // 通知依赖组件更新
      this.$root.$emit('cookie-changed', feature.cookieKey)
      
      // 显示操作成功提示
      this.$message({
        type: 'success',
        message: `${feature.name}功能已${feature.enabled ? '开启' : '关闭'}，页面将自动刷新`
      })
      
      // 延迟刷新页面，让用户看到提示信息
      setTimeout(() => {
        window.location.reload()
      }, 1500)
    }
  }
}
</script>

<style scoped>
.test-features {
  padding: 20px;
}
</style> 