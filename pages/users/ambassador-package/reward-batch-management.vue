<template>
  <el-container>
    <el-main>
      <h2 style="font-family: 'Microsoft YaHei',Arial,sans-serif;">
        激励包批次管理列表
      </h2>

      <el-form :inline="true" :model="filters">
        <el-form-item label="批次名称">
          <el-select clearable filterable v-model="filters.batch_id" @change="get_data" placeholder="<ALL>">
            <el-option
              v-for="batch in batch_list"
              :key="batch.batch_id"
              :label="`${batch.batch_id}-${batch.batch_name}`"
              :value="batch.batch_id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="状态">
          <el-select clearable filterable v-model="filters.status" @change="get_data" placeholder="请选择">
            <el-option v-for="(status_name, key) in status_dict" :key="key" :label="status_name" :value="key"> </el-option>
          </el-select>
        </el-form-item>

        <el-form-item style="margin-left: 40px;">
          <el-tooltip content="刷新" placement="right" :open-delay="500" :hide-after="2000">
            <el-button icon="el-icon-refresh-left" circle @click="handle_page_refresh"></el-button>
          </el-tooltip>
        </el-form-item>

        <el-form-item style="margin-left: 40px;">

          <el-tooltip
            content="添加"
            placement="right"
            :open-delay="500"
            :hide-after="2000"
          >
            <el-button type="primary" icon="el-icon-plus" circle @click="handleCreate"></el-button>

          </el-tooltip>
        </el-form-item>
      </el-form>

      <el-table :data="items" style="width: 100%">
        <el-table-column prop="id" label="批次ID"> </el-table-column>
        <el-table-column prop="batch_name" label="批次名称"> </el-table-column>
        <el-table-column prop="batch_total_amount" label="批次总额"> </el-table-column>
        <el-table-column prop="package_amount_str" label="激励包金额"> </el-table-column>
        <el-table-column prop="periods" label="激励包期数"> </el-table-column>
        <el-table-column prop="condition_str" label="结算条件" width="300"> 
          <template slot-scope="scope">
            <div class="condition-item">上月 refer 新交易人数 ≥ {{ scope.row.refer_trade_users }}</div>
            <div class="condition-item">上月 refer 交易金额 (USD) ≥ {{ scope.row.refer_trade_amount }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="total_users" label="发放人数">
          <template slot-scope="scope">
            <el-link
              :href="`/users/ambassador-package/reward-batch-usage?batch_id=${scope.row.id}`"
              type="primary"
              target="_blank"
              :underline="false"
            >
              {{ scope.row.total_users }}
            </el-link>
          </template>
        </el-table-column>
        <el-table-column prop="valid_days" label="领取有效期(天)"> </el-table-column>
        <el-table-column prop="release_at" label="发放时间" :formatter="row => $formatDate(row.release_at)">
        </el-table-column>
        <el-table-column prop="status" label="状态" :formatter="row => status_dict[row.status]"> </el-table-column>
        <el-table-column label="操作" width="180">
          <template slot-scope="scope">
            <span>
                <el-tooltip content="详情" placement="left" :open-delay="500" :hide-after="2000">
                  <el-button size="mini" type="primary" icon="el-icon-edit" circle @click="handleDetail(scope.row)"></el-button>
                </el-tooltip>
            </span>
            
            <span v-if="['CREATED'].includes(scope.row.status)">
              <el-tooltip content="审核通过" placement="right" :open-delay="500" :hide-after="2000">
                <el-button size="mini" type="success" icon="el-icon-check" circle
                           @click="handleApprove(scope.row)"></el-button>
              </el-tooltip>
            </span>
            <span>
              <el-tooltip content="审核拒绝" placement="right" :open-delay="500" :hide-after="2000">
                <el-button size="mini" type="danger" icon="el-icon-close" circle
                           @click="handleDelete(scope.row)" v-if="['CREATED'].includes(scope.row.status)"></el-button>
              </el-tooltip>
            </span>
            <span v-if="['AUDITED', 'RELEASED', 'CREATED'].includes(scope.row.status)">
              <el-tooltip content="停用" placement="right" :open-delay="500" :hide-after="2000">
                <el-button size="mini" type="info" icon="el-icon-turn-off" circle
                           @click="handleStop(scope.row)"></el-button>
              </el-tooltip>
            </span>
          </template>
        </el-table-column>
      </el-table>
      <el-dialog :visible.sync="dialogVisible" :title="action === this.DIALOG_CREATION ? '创建激励包' : '编辑/查看激励包'">
       <el-form :model="submit_data" :rules="rules" ref="batchForm">
         <el-form-item label="名称" prop="batch_name" required>
           
           <el-input v-model="submit_data.batch_name" placeholder="请输入" :disabled="edit_disabled && action === DIALOG_EDIT"></el-input>
         </el-form-item>
         <el-form-item label="币种" prop="asset" required>
            
           <el-select v-model="submit_data.asset" placeholder="请选择" :disabled="edit_disabled && action === DIALOG_EDIT">
             <el-option v-for="asset in assets" :key="asset" :label="asset" :value="asset"> </el-option>
           </el-select>
         </el-form-item>
         <el-form-item label="激励包金额" prop="package_amount" required>
            
           <el-input v-model.number="submit_data.package_amount" placeholder="请输入" type="number" :disabled="edit_disabled && action === DIALOG_EDIT"></el-input>
         </el-form-item>
         <el-form-item label="激励包期数" prop="periods" required>
            
           <el-input v-model.number="submit_data.periods" placeholder="请输入" type="number" :disabled="edit_disabled && action === DIALOG_EDIT"></el-input>
         </el-form-item>
         <el-form-item label="结算条件" required>
           <el-form-item> </el-form-item>
            <el-form-item label="上月 refer 新交易人数 ≥" prop="refer_trade_users" label-width="180px" style="margin-bottom: 10px;">
              
              <el-input v-model.number="submit_data.refer_trade_users" placeholder="请输入" type="number" :disabled="edit_disabled && action === DIALOG_EDIT"></el-input>
            </el-form-item>
            <el-form-item label="上月 refer 交易金额 (USD) ≥" prop="refer_trade_amount" label-width="220px" required>
               
               <el-input v-model.number="submit_data.refer_trade_amount" placeholder="请输入" type="number" :disabled="edit_disabled && action === DIALOG_EDIT"></el-input>
             </el-form-item>
         </el-form-item>
         <el-form-item label="领取有效期（天）" prop="valid_days" required>
            
           <el-input v-model.number="submit_data.valid_days" placeholder="请输入" :disabled="edit_disabled && action === DIALOG_EDIT"></el-input>
         </el-form-item>
         <el-form-item label="发放时间（整点）" prop="release_time" required>
           
           <el-date-picker
             v-model="submit_data.release_time"
             type="datetime"
             placeholder="选择日期时间"
             format="yyyy-MM-dd HH:00"
             
            :disabled="edit_disabled && action === DIALOG_EDIT"
           ></el-date-picker>
         </el-form-item>
         <el-form-item label="客群">
                 <span>
                   <UserGroupPicker
                     :tagGroups="tagGroups"
                     :dialogUserGroupMap="dialogUserGroupMap"
                     :edit_disabled="edit_disabled && action === DIALOG_EDIT"
                   ></UserGroupPicker>
                 </span>
                 <el-form-item
                   :inline="true"
                   v-if="submit_data.id"
                 >
                 人数统计：{{ submit_data.total_users }}
                   <el-tooltip
                     content="客群下载"
                     placement="right"
                     :open-delay="500"
                     :hide-after="2000"
                   >
                     <el-button
                       type="primary"
                       icon="el-icon-download"
                       circle
                       size="mini"
                       style="margin-left: 10px"
                       @click="downloadUser"
                     ></el-button>
                   </el-tooltip>
                 </el-form-item>
         </el-form-item>

       </el-form>
       <template slot="footer">
         <div class="dialog-footer">
           <el-button @click="dialogVisible = false">取消</el-button>
           <el-button type="primary" @click="submitForm" :disabled="edit_disabled && action === DIALOG_EDIT" v-preventReClick>确定</el-button>
         </div>
       </template>
     </el-dialog>
  <UserTagGroupDialog
        :tagGroups="tagGroups"
        :dialogUserGroupMap="dialogUserGroupMap"
      ></UserTagGroupDialog>
      <el-pagination :current-page.sync="filters.page" :page-size.sync="filters.limit" @size-change="handle_limit_change"
        @current-change="handle_page_change" :page-sizes="[50, 100]" :hide-on-single-page="true"
        layout="total, sizes, prev, pager, next, jumper" :total="total">
      </el-pagination>

      <el-backtop></el-backtop>
    </el-main>
  </el-container>

  
</template>

<script>
import UserGroupFilter from "~/components/UserGroupFilter";
import UserTagGroupDialog from "@/components/UserTagGroupDialog";
import UserGroupPicker from "@/components/UserGroupPicker";
import UserWebAuthn from "@/components/UserWebAuthn.vue";
import moment from 'moment';
const base_url = '/api/ambassador-package/batch';
export default {
  components: {
    UserWebAuthn,
    UserGroupFilter,
    UserTagGroupDialog,
    UserGroupPicker,
  },
  data() {
    // 添加自定义验证函数
    const validatePositiveNumber = (rule, value, callback) => {
      if (value <= 0) {
        callback(new Error('必须大于0'));
      } else {
        callback();
      }
    };

    const validatePositiveInteger = (rule, value, callback) => {
      if (value === '' || value === null) {
        callback(new Error('请输入数值'));
      } else if (!Number.isInteger(value) || value <= 0) {
        callback(new Error('请输入正整数'));
      } else {
        callback();
      }
    };

    const validateReleaseTime = (rule, value, callback) => {
      let now_time = moment();
      let release_time = moment(value);
      if (!value) {
        callback(new Error('请选择发放时间'));
      } 
      else if (release_time.isBefore(now_time)) {
        callback(new Error('发放时间不能早于当前时间'));
      }
      else {
        callback();
      }
    };
    return {
      filters: {
        batch_id: null,
        status: '',
        page: 1,
        limit: 50,
      },
      items: [],
      assets: [],
      total: 0,
      status_dict: {},
      dialogVisible: false,
      action: null,
      DIALOG_CREATION: "CREATION",
      DIALOG_EDIT: "EDIT",
      edit_disabled: false,
      batch_list: [],
      create_data: {
        id: null,
        batch_name: null,
        asset: null,
        package_amount: null,
        periods: null,
        valid_days: null,
        refer_trade_users: null,
        refer_trade_amount: null,
        release_time: null,
        group_ids: null,
        total_users: null,
      },
      submit_data: {},
      tagGroups: [],
      dialogUserGroupMap: {
          dialogUserGroup: false,
          dialogUserGroupTotal: 0,
          dialogUserGroupItems: [],
          group_types: {},
          dialogUserGroupLoading: false,
          dialogUserGroupFilters: {
            name: null,
            page: null,
            limit: 10,
          },
        },
      rules: {
        batch_name: [
          { required: true, message: '请输入批次名称', trigger: 'blur' }
        ],
        asset: [
          { required: true, message: '请输入币种', trigger: 'blur' }
        ],
        package_amount: [
          { required: true, message: '请输入激励包金额', trigger: 'blur' },
          { validator: validatePositiveNumber, trigger: 'blur' }
        ],
        periods: [
          { required: true, message: '请输入激励包期数', trigger: 'blur' },
          { type: 'number', message: '请输入数字', trigger: 'blur' },
          { validator: validatePositiveInteger, trigger: 'blur' }
        ],
        refer_trade_users: [
          { required: true, message: '请输入上月 refer 新交易人数', trigger: 'blur' },
          { type: 'number', message: '请输入数字', trigger: 'blur' },
          { validator: validatePositiveInteger, trigger: 'blur' }
        ],
        refer_trade_amount: [
          { required: true, message: '请输入上月 refer 交易金额', trigger: 'blur' },
          { validator: validatePositiveNumber, trigger: 'blur' }
        ],
        valid_days: [
          { required: true, message: '请输入领取有效期', trigger: 'blur' },
          { type: 'number', message: '请输入数字', trigger: 'blur' },
          { validator: validatePositiveInteger, trigger: 'blur' },
        ],
        release_time: [
          { required: true, message: '请选择发放时间', trigger: 'change' },
          { validator: validateReleaseTime, trigger: 'blur' }
        ],
      },
    };
  },
  methods: {
    handle_limit_change() {
      this.reset_page();
      this.get_data();
    },
    handle_page_change() {
      this.get_data();
    },
    handle_page_refresh() {
      this.reset_page();
      this.get_data();
    },
    downloadUser() {
      let id = this.submit_data.id;
      if (!id) {
        return;
      }
      let url = base_url + `/${id}/user-download`;
      this.$download_from_url(url, "batch-users.xlsx");
    },
    get_data() {
      this.loading = true;
      let search_params = _.omitBy(this.filters, (v) => v === undefined || v === null || v === "");

      this.$axios.get(base_url, { params: search_params }).then(res => {
        this.loading = false;
        if (res?.data?.code === 0) {
          let data = res.data.data;
          this.items = data.items;
          this.total = data.total;
          let extra = data.extra;
          this.batch_list = extra.batch_list;
          this.assets = extra.assets;
          this.status_dict = extra.statuses;
        }
        else {
          this.$message.error(res?.data?.data || '获取数据失败'  );
        }
      }).catch(() => {
        this.loading = false;
        this.$message.error('获取数据失败');
      });
    },
    handleApprove(row) {
      if (row.status !== 'CREATED') {
        this.$message.error('只有未审核的批次才能审核通过');
        return;
      }
      this.$confirm('确定审核通过该批次吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$axios.patch(base_url + `/${row.id}`, { status: 'AUDITED' }).then(res => {
          if (res?.data?.code === 0) {
            this.$message.success('审核通过成功');
            this.get_data();
          }
          else {
            this.$message.error(res?.data?.data || '审核失败');
          }
        }).catch(() => {
          this.$message.error('审核失败');
        });
      })
    },
    handleStop(row) {
      if (!['AUDITED', 'RELEASED'].includes(row.status)) {
        this.$message.error('只有已审核或已发放的批次才能停用');
        return;
      }
      this.$confirm('确定停用该批次吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$axios.patch(base_url + `/${row.id}`, { status: 'STOPPED' }).then(res => {
          if (res?.data?.code === 0) {
            this.$message.success('停用成功');
            this.get_data();
          }
          else {
            this.$message.error(res?.data?.data || '停用失败');
          }
        }).catch(() => {
          this.$message.error('停用失败');
        });
      })
    },
    handleDelete(row) {
      this.$confirm('确定拒绝审核该批次吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        if (row.status !== 'CREATED') {
          this.$message.error('只有未审核的批次才能拒绝审核');
          return;
        }
        this.$axios.delete(base_url + `/${row.id}`).then(res => {
          if (res?.data?.code === 0) {
            this.$message.success('操作成功');
          this.get_data();
        }
        else {
          this.$message.error(res?.data?.data || '操作失败');
        }
      }).catch(() => {
        this.$message.error('操作失败');
      });
    })
    },
    handleCreate() {
      this.dialogVisible = true;
      this.action = this.DIALOG_CREATION;
      this.submit_data = _.cloneDeep(this.create_data);
      this.tagGroups = [];
    },
    handleDetail(row) {
      if (row.status !== 'CREATED') {
        this.edit_disabled = true;
      }
      else {
        this.edit_disabled = false;
      }
      this.dialogVisible = true;
      this.action = this.DIALOG_EDIT;
      this.submit_data = _.cloneDeep(row);
      if (this.submit_data.release_time) {
        const date = moment(this.submit_data.release_time * 1000);
        this.submit_data.release_time = date.format('YYYY-MM-DD HH:00');
      }
      this.tagGroups = this.submit_data.tag_groups;
    },
    reset_page() {
      this.filters.page = 1;
    },
    submitForm() {
      this.$refs.batchForm.validate((valid) => {
        if (!valid) {
          return;
        }
        if (!this.tagGroups.length) {
          this.$message.error('请选择客群');
          return;
        }
        let groups = [];
        this.tagGroups.forEach((group) => {
          groups.push(group.id);
        });
        
        let submitData = _.cloneDeep(this.submit_data);
        if (submitData.release_time) {
          let fmt_release_time = moment(submitData.release_time).startOf('hour').unix();
          let now_time = moment().startOf('hour').unix();
          if (fmt_release_time <= now_time) {
            this.$message.error('发放时间不能早于当前时间');
            return;
          }
          submitData.release_time = fmt_release_time;
        }
        
        submitData.group_ids = JSON.stringify(groups);
        if (this.action === this.DIALOG_CREATION) {
          this.$axios.post(base_url, submitData).then(res => {
            if (res?.data?.code === 0) {
              this.$message.success('创建成功');
              this.dialogVisible = false;
              this.get_data();
            }
            else {
              this.$message.error(res?.data?.data || '创建失败');
            }
          }).catch(() => {
            this.$message.error('创建失败');
          });
        }
        else {
          this.$axios.put(base_url + `/${this.submit_data.id}`, submitData).then(res => {
            if (res?.data?.code === 0) {
              this.$message.success('编辑成功');
              this.dialogVisible = false;
              this.get_data();
            }
            else {
              this.$message.error(res?.data?.data || '编辑失败');
            }
          }).catch(() => {
            this.$message.error('编辑失败');
          });
        }
      });
    },
  },
  mounted() {
    this.get_data();
  },
};
</script>

<style scoped>
.condition-item {
  line-height: 1.5;
  margin: 2px 0;
}
</style> 