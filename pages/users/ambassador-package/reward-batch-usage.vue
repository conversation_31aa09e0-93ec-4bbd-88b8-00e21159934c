<template>
  <el-container>
    <el-main>
      <h2 style="font-family: 'Microsoft YaHei',Arial,sans-serif;">
        激励包使用明细
      </h2>

      <el-form :inline="true" :model="filters">
        <el-form-item label="批次名称">
          <el-select filterable v-model="filters.batch_id"  placeholder="<ALL>" style="width: 200px;">
            <el-option label="<ALL>" value="ALL" />
            <el-option
              v-for="batch in batch_list"
              :key="batch.batch_id"
              :label="`${batch.batch_id}-${batch.batch_name}`"
              :value="batch.batch_id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="用户ID" prop="user_id">
          <UserSearch v-model="filters.user_id"></UserSearch>
        </el-form-item>
        <el-form-item label="大使类型" prop="ambassador_type">
          <el-select clearable filterable v-model="filters.ambassador_type"  placeholder="<ALL>" style="width: 150px;">
            <el-option v-for="(name, key) in ambassador_type_dic" :key="key" :label="name" :value="key"> </el-option>
          </el-select>
        </el-form-item>
         <el-form-item label="激励包对用户状态" prop="status">
          <el-select clearable filterable v-model="filters.status"  placeholder="<ALL>" style="width: 180px;">
            <el-option v-for="(name, key) in status_dic" :key="key" :label="name" :value="key"> </el-option>
          </el-select>
        </el-form-item>

        <el-form-item style="margin-left: 10px;">
          <el-tooltip content="刷新" placement="top" :open-delay="500">
            <el-button icon="el-icon-refresh-left" circle @click="handle_page_refresh"></el-button>
          </el-tooltip>
        </el-form-item>

        <el-form-item>
          <el-tooltip content="导出" placement="top" :open-delay="500">
             <el-button type="primary" icon="el-icon-download" circle @click="handleExport"></el-button>
          </el-tooltip>
        </el-form-item>
      </el-form>

      <el-table :data="items" v-loading="loading" style="width: 100%">
        <el-table-column prop="user_id" label="用户ID" width="100">
          <template slot-scope="scope">
            <el-link
              :href="'/users/user-details?id=' + scope.row.user_id"
              type="primary"
              target="_blank"
              :underline="false"
              style="
              width: 100%;
              font-weight: normal;
              display: inline-block;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            "
            >
              {{ scope.row.user_id }}
            </el-link>
          </template>
           </el-table-column>
        <el-table-column prop="email" label="邮箱" width="180"> 
          <template slot-scope="scope">
            <el-link
              :href="'/users/user-details?id=' + scope.row.user_id"
              type="primary"
              target="_blank"
              :underline="false"
              style="
              width: 100%;
              font-weight: normal;
              display: inline-block;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            "
            >
              {{ scope.row.email }}
            </el-link>
          </template>
        </el-table-column>
        <el-table-column prop="ambassador_type" label="大使类型" width="120"> </el-table-column>
        <el-table-column prop="id" label="激励包ID" width="100"> </el-table-column>
        <el-table-column prop="batch_name" label="批次名称" width="150"> </el-table-column>
        <el-table-column prop="batch_package_amount_str" label="激励包金额" width="150"> </el-table-column>
        <el-table-column prop="batch_each_period_amount_str" label="激励包每期金额" width="150"> </el-table-column>
        <el-table-column prop="batch_periods" label="激励包期数" width="120"> </el-table-column>
        <el-table-column prop="released_periods" label="已发放期数" width="100"> </el-table-column>
        <el-table-column prop="total_release_amount_str" label="收到激励总额" width="150"> </el-table-column>
        <el-table-column prop="total_refer_users" label="refer交易人数" width="120"> </el-table-column>
        <el-table-column prop="total_refer_amount" label="refer交易金额（USD）" width="160"> </el-table-column>
        <el-table-column prop="status" label="用户激励包状态" width="140"> </el-table-column>
        <el-table-column prop="claim_time" label="领取时间" width="160" :formatter="row => $formatDate(row.claim_time)"> </el-table-column>
        <el-table-column prop="updated_at" label="更新时间" width="160" :formatter="row => $formatDate(row.updated_at)"> </el-table-column>
        <el-table-column label="操作" width="80">
           <template slot-scope="scope">
             <el-button size="mini" type="danger" @click="handleStopUse(scope.row)" :disabled="scope.row.status === '已停用'">停用</el-button>
           </template>
        </el-table-column>
      </el-table>

      <el-pagination
        :current-page.sync="filters.page"
        :page-size.sync="filters.limit"
        @size-change="handle_limit_change"
        @current-change="handle_page_change"
        :page-sizes="[50, 100, 200]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total">
      </el-pagination>

      <el-backtop></el-backtop>
    </el-main>
  </el-container>
</template>

<script>
const base_url = '/api/ambassador-package/user/packages'; 
import UserSearch from "../../../components/user/UserSearch";

export default {
  components: { UserSearch },
  name: 'RewardBatchUsage',
  data() {
    return {
      filters: {
        batch_id: null,
        user_id: null,
        ambassador_type: null,
        status: null,
        page: 1,
        limit: 50,
      },
      items: [],
      total: 0,
      loading: false,
      batch_list: [],
      status_dic: {},
      ambassador_type_dic: {},
      batch_id: null,
    };
  },
  methods: {
    handle_limit_change() {
      this.reset_page();
      this.get_data();
    },
    handle_page_change() {
      this.get_data();
    },
    handle_search() {
       this.reset_page();
       this.get_data();
    },
     handle_page_refresh() {
      this.reset_page();
      this.get_data();
    },
    reset_page() {
      this.filters.page = 1;
    },
    
    get_data() {
      this.loading = true;
      let search_params = _.omitBy(this.filters, (v) => v === undefined || v === null || v === "");
      if (search_params.batch_id === 'ALL') {
        search_params.batch_id = 0
      }
      this.$axios.get(base_url, { params: search_params }).then(res => {
        this.loading = false;
        if (res?.data?.code === 0) {
          let data = res.data.data;
          this.items = data.items || [];
          this.total = data.total || 0;
          let extra = data.extra || {};
          this.batch_list = extra.batch_list || [];
          this.status_dic = extra.status_dic || {};
          this.ambassador_type_dic = extra.ambassador_type_dic || {};
          this.batch_id = data.batch_id;
          if (this.batch_id === 0) {
            this.filters.batch_id = 'ALL';
          } else {
            this.filters.batch_id = this.batch_id;
          }
          this.$router.replace({ query: { batch_id: this.batch_id } });
        } else {
          this.$message.error(res?.data?.message || '获取使用明细失败');
          this.items = [];
          this.total = 0;
        }
      }).catch((error) => {
        this.loading = false;
        this.$message.error('获取使用明细请求失败: ' + error.message);
        this.items = [];
        this.total = 0;
      });
    },
    handleExport() {

      let filename = 'reward-batch-usage.xlsx';

      let params = { ...this.filters }
      if (this.filters.batch_id === 'ALL') {
        params.batch_id = 0;
      }
      params['export'] = true;
      this.$download_from_url(base_url, filename, params);
    },
    handleStopUse(row) {

       this.$confirm(`确定停用用户 ${row.user_id} 的该激励包吗？`, '提示', { type: 'warning' })
        .then(() => {
          this.$axios.patch(`/api/ambassador-package/user/package/${row.id}/stop`) 
            .then(res => {
              if (res?.data?.code === 0) {
                this.$message.success('停用成功');
                this.get_data(); 
              } else {
                this.$message.error(res?.data?.data || '停用失败');
              }
            }).catch(err => this.$message.error('停用请求失败: ' + err.message));
        }).catch(() => {}); 
    }
  },
  mounted() {
    this.filters.batch_id = this.$route.query.batch_id;
    this.get_data();       
  },
};
</script>
