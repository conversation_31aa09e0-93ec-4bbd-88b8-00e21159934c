<template>
  <div>
    <h2>激励包批次统计列表
    <el-tooltip placement="right" :open-delay="500">
          <div slot="content">
            <p>批次ID：按激励包批次配置顺序自动生成的</p>
            <p>批次名称：添加激励包时配置，该名称只展示在后台，不展示在前端</p>
            <p>批次总额：该批次激励包的总额，创建激励包配置单个激励包金额，这里的总额=单个激励包金额*发放人数，单位币种按配置的展示</p>
            <p>激励包金额：创建激励包时配置的单个激励包金额</p>
            <p>激励包每期金额：激励包金额/期数</p>
            <p>激励包期数：添加激励包时配置的总期数</p>
            <p>已发放期数：到最近一次更新时间，该批次激励包已经结算的次数</p>
            <p>已发放总额：到最近一次更新时间，该批次激励包实际已经发放给用户的总金额</p>
            <p>激励包发放时间：该批次激励包实际发放的时间；如果在配置的发放时间前审核，就是配置的发放时间，如果在配置的发放时间后审核，那实际的发放时间改为审核后的下一个整点</p>
            <p>首期发放时间：该批次激励包首次发放的时间，精确到日即可</p>
            <p>发放人数：该批次激励包圈群发放的人数，点击新开页面跳至【激励包使用明细】并筛选这批次</p>
            <p>领取人数：到最近一次更新时间，该激励包已领取人数</p>
            <p>收到激励人数：到最近一次更新时间，对该批次激励包实际有收到激励的人数</p>
            <p>refer交易总人数：所有领取激励包的大使，从激励包开始统计数据后的refer交易总人数</p>
            <p>refer交易总额（USD）：所有领取激励包的大使，从激励包开始统计数据后的refer交易总额，折算成USD</p>
            <p>更新时间：该激励包最近一次更新时间，每天更新即可；到激励包最后一期发放完之后不再更新</p>
          </div>
          <i class="el-icon-question"></i>
        </el-tooltip>
      </h2>
    <el-form :inline="true" :model="filters">
        <el-form-item label="批次名称">
          <el-select clearable filterable v-model="filters.batch_id" @change="get_data" placeholder="<ALL>">
            <el-option
              v-for="batch in batch_list"
              :key="batch.batch_id"
              :label="`${batch.batch_id}-${batch.batch_name}`"
              :value="batch.batch_id"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-refresh" circle @click="handle_page_refresh"></el-button>
        </el-form-item>

        <el-form-item>
          <el-tooltip content="导出" placement="top" :open-delay="500">
             <el-button type="primary" icon="el-icon-download" circle @click="handleExport"></el-button>
          </el-tooltip>
        </el-form-item>
      </el-form>

      <el-table :data="items" style="width: 100%">
        <el-table-column prop="id" label="批次ID"> </el-table-column>
        <el-table-column prop="batch_name" label="批次名称"> </el-table-column>
        <el-table-column prop="batch_total_amount" label="批次总额"> </el-table-column>
        <el-table-column prop="package_amount_str" label="激励包金额"> </el-table-column>
        <el-table-column prop="batch_each_period_amount_str" label="激励包每期金额"> </el-table-column>
        <el-table-column prop="periods" label="激励包期数"> </el-table-column>
        <el-table-column prop="released_periods" label="已发放期数"> </el-table-column>
        <el-table-column prop="total_release_amount_str" label="已发放总额"> </el-table-column>
        <el-table-column prop="release_at" label="激励包发放时间" :formatter="row => $formatDate(row.release_at)">
        </el-table-column>
        <el-table-column prop="first_release_time" label="首期发放时间" :formatter="row => $formatDate(row.first_release_time)"></el-table-column>
        <el-table-column prop="total_users" label="发放人数">
          <template slot-scope="scope">
            <el-link
              :href="`/users/ambassador-package/reward-batch-usage?batch_id=${scope.row.id}`"
              type="primary"
              target="_blank"
              :underline="false"
            >
              {{ scope.row.total_users }}
            </el-link>
          </template>
        </el-table-column>
        <el-table-column prop="claimed_users" label="领取人数"> </el-table-column>
        <el-table-column prop="rewarded_users" label="收到激励人数"> </el-table-column>
        <el-table-column prop="total_refer_users" label="refer交易总人数"> </el-table-column>
        <el-table-column prop="total_refer_amount" label="refer交易总金额"> </el-table-column>
        <el-table-column prop="updated_at" label="更新时间" :formatter="row => $formatDate(row.updated_at)">
        </el-table-column>
      </el-table>
      
      <el-pagination :current-page.sync="filters.page" :page-size.sync="filters.limit" @size-change="handle_limit_change"
        @current-change="handle_page_change" :page-sizes="[50, 100]" :hide-on-single-page="true"
        layout="total, sizes, prev, pager, next, jumper" :total="total">
      </el-pagination>

      <el-backtop></el-backtop>
  </div>
</template>

<script>

const base_url = '/api/ambassador-package/batch/stats';
export default {
  
  data() {
    return {
      filters: {
        batch_id: null,
        page: 1,
        limit: 50,
      },
      items: [],
      assets: [],
      total: 0,
      status_dict: {},
      batch_list: [],
    };
  },
  methods: {
    handle_limit_change() {
      this.reset_page();
      this.get_data();
    },
    handle_page_change() {
      this.get_data();
    },
    handle_page_refresh() {
      this.reset_page();
      this.get_data();
    },
    handleExport() {
      let filename = 'reward-batch-statistics.xlsx';
      let params = { ...this.filters }
      params['export'] = true;
      this.$download_from_url(base_url, filename, params);
    },
    get_data() {
      this.loading = true;
      let search_params = _.omitBy(this.filters, (v) => v === undefined || v === null || v === "");

      this.$axios.get(base_url, { params: search_params }).then(res => {
        this.loading = false;
        if (res?.data?.code === 0) {
          let data = res.data.data;
          this.items = data.items;
          this.total = data.total;
          let extra = data.extra;
          this.batch_list = extra.batch_list;
        }
        else {
          this.$message.error(res?.data?.data || '获取数据失败'  );
        }
      }).catch(() => {
        this.loading = false;
        this.$message.error('获取数据失败');
      });
    },
    reset_page() {
      this.filters.page = 1;
    },
  },
  mounted() {
    this.get_data();
  },
};
</script>

<style scoped>
.condition-item {
  line-height: 1.5;
  margin: 2px 0;
}
</style> 