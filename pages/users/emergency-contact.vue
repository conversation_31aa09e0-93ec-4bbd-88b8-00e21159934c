<template>
  <el-container>
    <el-main>
      <h2 style="font-family: 'Microsoft YaHei',Arial,sans-serif;">
        紧急联系人列表
      </h2>
      <el-divider></el-divider>
      <el-table
        :data="items"
        style="width: 100%">
        <el-table-column
          prop="id"
          label="ID">
        </el-table-column>

        <el-table-column
          prop="user_id"
          label="用户">
          <template slot-scope="scope">
            <a :href="scope.row.user_id | user_link"
               target="_blank"
               class="buttonText">
              {{ scope.row.user_email }}
            </a>
          </template>
        </el-table-column>

        <el-table-column
          prop="name"
          label="联系人姓名">
        </el-table-column>

        <el-table-column
          prop="email"
          label="联系人邮箱">
        </el-table-column>

        <el-table-column
          prop="user_message"
          label="留言">
        </el-table-column>

        <el-table-column
          prop="dormancy_period"
          :formatter="row => dormancy_periods[row.dormancy_period]"
          label="账号休眠期"
        >
        </el-table-column>

      </el-table>

      <el-pagination :current-page.sync="search_data.page"
                     :page-size.sync="search_data.limit"
                     @size-change="search"
                     @current-change="search"
                     :page-size="50"
                     :page-sizes="[10, 50, 100, 200, 500]"
                     :hide-on-single-page="true"
                     layout="total, sizes, prev, pager, next, jumper"
                     :total="total">
      </el-pagination>

      <el-backtop></el-backtop>
    </el-main>
  </el-container>
</template>


<script>

const binding_url = "/api/emergency-contact";

export default {
  watchQuery: ['user_id', 'page', 'limit',],
  key: to => to.fullPath,
  asyncData({app, query}) {
    return app.$axios["get"](binding_url, {params: query})
      .then((res) => {
        return {
          total: res.data.data.total,
          items: res.data.data.items,
          dormancy_periods: res.data.data.extra.dormancy_periods,
          search_data: _.clone(query),
        }
      })
      .catch((e) => {
      })
  },
  methods: {
    search() {
      this.$router.push({path: this.$route.path, query: this.search_data});
    },
  },
  data() {
    return {
      dormancy_periods: {},
      total: null,
      search_data: {
        page: 1,
        limit: 50,
      },
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() < Date.now();
        }
      }
    }
  }
}

</script>
