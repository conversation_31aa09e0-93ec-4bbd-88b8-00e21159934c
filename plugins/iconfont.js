/**
 * Iconfont 插件
 * 用于动态加载 iconfont.js 文件
 */

let iconfontLoaded = false;

export function loadIconfont() {
  // 如果已经加载过，直接返回
  if (iconfontLoaded) {
    return Promise.resolve();
  }

  // 检查是否已经存在 script 标签
  if (document.querySelector('script[src="/scripts/iconfont.js"]')) {
    iconfontLoaded = true;
    return Promise.resolve();
  }

  return new Promise((resolve, reject) => {
    const script = document.createElement('script');
    script.src = '/scripts/iconfont.js';
    
    script.onload = () => {
      console.log('Iconfont loaded successfully');
      iconfontLoaded = true;
      resolve();
    };
    
    script.onerror = () => {
      console.error('Failed to load iconfont.js');
      reject(new Error('Failed to load iconfont.js'));
    };
    
    document.head.appendChild(script);
  });
}

export default {
  install(Vue) {
    // 将 loadIconfont 方法添加到 Vue 原型上
    Vue.prototype.$loadIconfont = loadIconfont;
  }
} 